{"ast": null, "code": "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\nconst findHTMLForm = form => {\n  let currentForm;\n  if (typeof form === 'string') {\n    currentForm = document.querySelector(form);\n  } else {\n    currentForm = form;\n  }\n  if (!currentForm || currentForm.nodeName !== 'FORM') {\n    throw 'The 3rd parameter is expected to be the HTML form element or the style selector of form';\n  }\n  return currentForm;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = (serviceID, templateID, form, userID) => {\n  const uID = userID || store._userID;\n  const currentForm = findHTMLForm(form);\n  validateParams(uID, serviceID, templateID);\n  const formData = new FormData(currentForm);\n  formData.append('lib_version', '3.2.0');\n  formData.append('service_id', serviceID);\n  formData.append('template_id', templateID);\n  formData.append('user_id', uID);\n  return sendPost('/api/v1.0/email/send-form', formData);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}