{"ast": null, "code": "// Email service for sending <PERSON><PERSON> using EmailJS\nimport emailjs from 'emailjs-com';\n// EmailJS Configuration\nconst EMAILJS_CONFIG = {\n  SERVICE_ID: 'service_innovaid',\n  TEMPLATE_ID: 'template_otp',\n  PUBLIC_KEY: 'YOUR_PUBLIC_KEY' // Replace with your EmailJS public key\n};\nexport const sendOTPEmail = async request => {\n  try {\n    console.log('📧 Sending OTP Email to:', request.email);\n\n    // For development/testing - show OTP in console\n    console.log(`🔐 OTP Code: ${request.otp}`);\n    console.log(`⏰ Expires: ${request.expiryTime}`);\n\n    // EmailJS template parameters\n    const templateParams = {\n      to_email: request.email,\n      to_name: request.name || 'User',\n      otp_code: request.otp,\n      expiry_time: request.expiryTime,\n      platform_name: 'InnovAid for SECE',\n      college_name: 'Sri Eshwar College of Engineering'\n    };\n\n    // Try to send email via EmailJS\n    try {\n      const result = await emailjs.send(EMAILJS_CONFIG.SERVICE_ID, EMAILJS_CONFIG.TEMPLATE_ID, templateParams, EMAILJS_CONFIG.PUBLIC_KEY);\n      if (result.status === 200) {\n        console.log('✅ Email sent successfully via EmailJS');\n        return true;\n      } else {\n        console.warn('⚠️ EmailJS returned non-200 status:', result.status);\n        return false;\n      }\n    } catch (emailError) {\n      console.warn('⚠️ EmailJS failed, using fallback method:', emailError);\n\n      // Fallback: For development, we'll simulate email sending\n      // In production, you could integrate with another email service here\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      console.log('📧 Email simulated (EmailJS not configured)');\n      console.log('🔗 To configure real emails:');\n      console.log('1. Sign up at https://emailjs.com');\n      console.log('2. Create email service and template');\n      console.log('3. Update EMAILJS_CONFIG in emailService.ts');\n      return true; // Return true for development\n    }\n  } catch (error) {\n    console.error('❌ Error sending OTP email:', error);\n    return false;\n  }\n};\nexport const getEmailTemplate = (otp, expiryTime) => {\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\n        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }\n        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }\n        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }\n        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎓 InnovAid for SECE</h1>\n          <p>Email Verification Required</p>\n        </div>\n        <div class=\"content\">\n          <h2>Welcome to InnovAid!</h2>\n          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>\n          \n          <div class=\"otp-box\">\n            <p>Your verification code is:</p>\n            <div class=\"otp-code\">${otp}</div>\n          </div>\n          \n          <div class=\"warning\">\n            <strong>⚠️ Important:</strong>\n            <ul>\n              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>\n              <li>Do not share this code with anyone</li>\n              <li>If you didn't request this, please ignore this email</li>\n            </ul>\n          </div>\n          \n          <p>Once verified, you'll have access to all campus utilities including:</p>\n          <ul>\n            <li>📚 Library Management</li>\n            <li>🏠 Hostel Services</li>\n            <li>📅 Timetable Access</li>\n            <li>🚀 Tech Events & Updates</li>\n            <li>📋 Polls & Forms</li>\n            <li>🔍 Lost & Found</li>\n          </ul>\n          \n          <div class=\"footer\">\n            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n};", "map": {"version": 3, "names": ["emailjs", "EMAILJS_CONFIG", "SERVICE_ID", "TEMPLATE_ID", "PUBLIC_KEY", "sendOTPEmail", "request", "console", "log", "email", "otp", "expiryTime", "templateParams", "to_email", "to_name", "name", "otp_code", "expiry_time", "platform_name", "college_name", "result", "send", "status", "warn", "emailError", "Promise", "resolve", "setTimeout", "error", "getEmailTemplate"], "sources": ["B:/ZERO DAY/ZeroDay-Innovaid/beautiful-react-ui/src/services/emailService.ts"], "sourcesContent": ["// Email service for sending <PERSON><PERSON> using EmailJS\nimport emailjs from 'emailjs-com';\n\nexport interface EmailOTPRequest {\n  email: string;\n  otp: string;\n  name?: string;\n  expiryTime: string;\n}\n\n// EmailJS Configuration\nconst EMAILJS_CONFIG = {\n  SERVICE_ID: 'service_innovaid',\n  TEMPLATE_ID: 'template_otp',\n  PUBLIC_KEY: 'YOUR_PUBLIC_KEY' // Replace with your EmailJS public key\n};\n\nexport const sendOTPEmail = async (request: EmailOTPRequest): Promise<boolean> => {\n  try {\n    console.log('📧 Sending OTP Email to:', request.email);\n\n    // For development/testing - show OTP in console\n    console.log(`🔐 OTP Code: ${request.otp}`);\n    console.log(`⏰ Expires: ${request.expiryTime}`);\n\n    // EmailJS template parameters\n    const templateParams = {\n      to_email: request.email,\n      to_name: request.name || 'User',\n      otp_code: request.otp,\n      expiry_time: request.expiryTime,\n      platform_name: 'InnovAid for SECE',\n      college_name: 'Sri Eshwar College of Engineering'\n    };\n\n    // Try to send email via EmailJS\n    try {\n      const result = await emailjs.send(\n        EMAILJS_CONFIG.SERVICE_ID,\n        EMAILJS_CONFIG.TEMPLATE_ID,\n        templateParams,\n        EMAILJS_CONFIG.PUBLIC_KEY\n      );\n\n      if (result.status === 200) {\n        console.log('✅ Email sent successfully via EmailJS');\n        return true;\n      } else {\n        console.warn('⚠️ EmailJS returned non-200 status:', result.status);\n        return false;\n      }\n    } catch (emailError) {\n      console.warn('⚠️ EmailJS failed, using fallback method:', emailError);\n\n      // Fallback: For development, we'll simulate email sending\n      // In production, you could integrate with another email service here\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      console.log('📧 Email simulated (EmailJS not configured)');\n      console.log('🔗 To configure real emails:');\n      console.log('1. Sign up at https://emailjs.com');\n      console.log('2. Create email service and template');\n      console.log('3. Update EMAILJS_CONFIG in emailService.ts');\n\n      return true; // Return true for development\n    }\n  } catch (error) {\n    console.error('❌ Error sending OTP email:', error);\n    return false;\n  }\n};\n\nexport const getEmailTemplate = (otp: string, expiryTime: string): string => {\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\n        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }\n        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }\n        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }\n        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎓 InnovAid for SECE</h1>\n          <p>Email Verification Required</p>\n        </div>\n        <div class=\"content\">\n          <h2>Welcome to InnovAid!</h2>\n          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>\n          \n          <div class=\"otp-box\">\n            <p>Your verification code is:</p>\n            <div class=\"otp-code\">${otp}</div>\n          </div>\n          \n          <div class=\"warning\">\n            <strong>⚠️ Important:</strong>\n            <ul>\n              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>\n              <li>Do not share this code with anyone</li>\n              <li>If you didn't request this, please ignore this email</li>\n            </ul>\n          </div>\n          \n          <p>Once verified, you'll have access to all campus utilities including:</p>\n          <ul>\n            <li>📚 Library Management</li>\n            <li>🏠 Hostel Services</li>\n            <li>📅 Timetable Access</li>\n            <li>🚀 Tech Events & Updates</li>\n            <li>📋 Polls & Forms</li>\n            <li>🔍 Lost & Found</li>\n          </ul>\n          \n          <div class=\"footer\">\n            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n};\n"], "mappings": "AAAA;AACA,OAAOA,OAAO,MAAM,aAAa;AASjC;AACA,MAAMC,cAAc,GAAG;EACrBC,UAAU,EAAE,kBAAkB;EAC9BC,WAAW,EAAE,cAAc;EAC3BC,UAAU,EAAE,iBAAiB,CAAC;AAChC,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAOC,OAAwB,IAAuB;EAChF,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,OAAO,CAACG,KAAK,CAAC;;IAEtD;IACAF,OAAO,CAACC,GAAG,CAAC,gBAAgBF,OAAO,CAACI,GAAG,EAAE,CAAC;IAC1CH,OAAO,CAACC,GAAG,CAAC,cAAcF,OAAO,CAACK,UAAU,EAAE,CAAC;;IAE/C;IACA,MAAMC,cAAc,GAAG;MACrBC,QAAQ,EAAEP,OAAO,CAACG,KAAK;MACvBK,OAAO,EAAER,OAAO,CAACS,IAAI,IAAI,MAAM;MAC/BC,QAAQ,EAAEV,OAAO,CAACI,GAAG;MACrBO,WAAW,EAAEX,OAAO,CAACK,UAAU;MAC/BO,aAAa,EAAE,mBAAmB;MAClCC,YAAY,EAAE;IAChB,CAAC;;IAED;IACA,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMpB,OAAO,CAACqB,IAAI,CAC/BpB,cAAc,CAACC,UAAU,EACzBD,cAAc,CAACE,WAAW,EAC1BS,cAAc,EACdX,cAAc,CAACG,UACjB,CAAC;MAED,IAAIgB,MAAM,CAACE,MAAM,KAAK,GAAG,EAAE;QACzBf,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,OAAO,IAAI;MACb,CAAC,MAAM;QACLD,OAAO,CAACgB,IAAI,CAAC,qCAAqC,EAAEH,MAAM,CAACE,MAAM,CAAC;QAClE,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOE,UAAU,EAAE;MACnBjB,OAAO,CAACgB,IAAI,CAAC,2CAA2C,EAAEC,UAAU,CAAC;;MAErE;MACA;MACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDnB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1DD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGA,CAACnB,GAAW,EAAEC,UAAkB,KAAa;EAC3E,OAAO;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoCD,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA,qDAAqDC,UAAU;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}