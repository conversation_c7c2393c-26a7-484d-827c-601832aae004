[{"B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\index.tsx": "1", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\App.tsx": "2", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\SimpleLogin.tsx": "3", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\UserDashboard.tsx": "4", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\AdminDashboard.tsx": "5", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\auth.ts": "6", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\config.ts": "7", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentTimetable.tsx": "8", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentAnnouncements.tsx": "9", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentLostFound.tsx": "10", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentHostelComplaints.tsx": "11", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\TimetableManagement.tsx": "12", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\AnnouncementManagement.tsx": "13", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\HostelManagement.tsx": "14", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\StudentManagement.tsx": "15", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\LostFoundManagement.tsx": "16", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\services\\lostFoundService.ts": "17", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\services\\socketService.ts": "18", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\registrations.ts": "19", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\RegistrationManagement.tsx": "20", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\announcements.ts": "21", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\lostFound.ts": "22", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\hostelComplaints.ts": "23", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\SkillExchange.tsx": "24", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\skillExchange.ts": "25", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\CourseDetailModal.tsx": "26", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\CreateCourseForm.tsx": "27", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\AdminTechEvents.tsx": "28", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\emailNotifications.ts": "29", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\techEvents.ts": "30", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\notifications.ts": "31", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\TechUpdates.tsx": "32", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\PollsManagement.tsx": "33", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\polls.ts": "34", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentPolls.tsx": "35", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\PollsFormsManagement.tsx": "36", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentPollsForms.tsx": "37", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\TechUpdatesManagement.tsx": "38", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\techUpdates.ts": "39", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\TechUpdatesView.tsx": "40", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\realtimeTechEvents.ts": "41", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentLibrary.tsx": "42", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\realtimeTimetable.ts": "43", "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\services\\emailService.ts": "44"}, {"size": 230, "mtime": 1753455416370, "results": "45", "hashOfConfig": "46"}, {"size": 3291, "mtime": 1753495052004, "results": "47", "hashOfConfig": "46"}, {"size": 26776, "mtime": 1753605001241, "results": "48", "hashOfConfig": "46"}, {"size": 43049, "mtime": 1753511404354, "results": "49", "hashOfConfig": "46"}, {"size": 19586, "mtime": 1753511404350, "results": "50", "hashOfConfig": "46"}, {"size": 23747, "mtime": 1753511404364, "results": "51", "hashOfConfig": "46"}, {"size": 1603, "mtime": 1753468137819, "results": "52", "hashOfConfig": "46"}, {"size": 15784, "mtime": 1753511404360, "results": "53", "hashOfConfig": "46"}, {"size": 30634, "mtime": 1753503355843, "results": "54", "hashOfConfig": "46"}, {"size": 21714, "mtime": 1753503355845, "results": "55", "hashOfConfig": "46"}, {"size": 21895, "mtime": 1753503355844, "results": "56", "hashOfConfig": "46"}, {"size": 31690, "mtime": 1753511404356, "results": "57", "hashOfConfig": "46"}, {"size": 23234, "mtime": 1753455416361, "results": "58", "hashOfConfig": "46"}, {"size": 24942, "mtime": 1753456459050, "results": "59", "hashOfConfig": "46"}, {"size": 13442, "mtime": 1753478565554, "results": "60", "hashOfConfig": "46"}, {"size": 23067, "mtime": 1753478574601, "results": "61", "hashOfConfig": "46"}, {"size": 5560, "mtime": 1753447202644, "results": "62", "hashOfConfig": "46"}, {"size": 5553, "mtime": 1753468137821, "results": "63", "hashOfConfig": "46"}, {"size": 8399, "mtime": 1753455416369, "results": "64", "hashOfConfig": "46"}, {"size": 15282, "mtime": 1753455416364, "results": "65", "hashOfConfig": "46"}, {"size": 12004, "mtime": 1753455416366, "results": "66", "hashOfConfig": "46"}, {"size": 6771, "mtime": 1753478574605, "results": "67", "hashOfConfig": "46"}, {"size": 5971, "mtime": 1753456459054, "results": "68", "hashOfConfig": "46"}, {"size": 27132, "mtime": 1753503355842, "results": "69", "hashOfConfig": "46"}, {"size": 10672, "mtime": 1753462228294, "results": "70", "hashOfConfig": "46"}, {"size": 13953, "mtime": 1753462228292, "results": "71", "hashOfConfig": "46"}, {"size": 15628, "mtime": 1753462228292, "results": "72", "hashOfConfig": "46"}, {"size": 29233, "mtime": 1753507021343, "results": "73", "hashOfConfig": "46"}, {"size": 17623, "mtime": 1753478565557, "results": "74", "hashOfConfig": "46"}, {"size": 9232, "mtime": 1753478565559, "results": "75", "hashOfConfig": "46"}, {"size": 12016, "mtime": 1753494474828, "results": "76", "hashOfConfig": "46"}, {"size": 21109, "mtime": 1753503355849, "results": "77", "hashOfConfig": "46"}, {"size": 21758, "mtime": 1753488398846, "results": "78", "hashOfConfig": "46"}, {"size": 24438, "mtime": 1753491094888, "results": "79", "hashOfConfig": "46"}, {"size": 17711, "mtime": 1753503355846, "results": "80", "hashOfConfig": "46"}, {"size": 42833, "mtime": 1753491363759, "results": "81", "hashOfConfig": "46"}, {"size": 37364, "mtime": 1753503355847, "results": "82", "hashOfConfig": "46"}, {"size": 25634, "mtime": 1753493169695, "results": "83", "hashOfConfig": "46"}, {"size": 16616, "mtime": 1753494245585, "results": "84", "hashOfConfig": "46"}, {"size": 15470, "mtime": 1753492299096, "results": "85", "hashOfConfig": "46"}, {"size": 9227, "mtime": 1753503355850, "results": "86", "hashOfConfig": "46"}, {"size": 44999, "mtime": 1753507021345, "results": "87", "hashOfConfig": "46"}, {"size": 12451, "mtime": 1753511404366, "results": "88", "hashOfConfig": "46"}, {"size": 3839, "mtime": 1753604972244, "results": "89", "hashOfConfig": "46"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mj89lb", {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\index.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\App.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\SimpleLogin.tsx", ["222", "223"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\UserDashboard.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\AdminDashboard.tsx", ["224", "225", "226"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\auth.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\config.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentTimetable.tsx", ["227", "228"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentAnnouncements.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentLostFound.tsx", ["229"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentHostelComplaints.tsx", ["230", "231", "232", "233", "234"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\TimetableManagement.tsx", ["235", "236", "237", "238", "239"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\AnnouncementManagement.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\HostelManagement.tsx", ["240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\StudentManagement.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\LostFoundManagement.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\services\\lostFoundService.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\services\\socketService.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\registrations.ts", ["253", "254", "255", "256", "257", "258"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\RegistrationManagement.tsx", ["259"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\announcements.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\lostFound.ts", ["260"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\hostelComplaints.ts", ["261"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\SkillExchange.tsx", ["262"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\skillExchange.ts", ["263", "264"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\CourseDetailModal.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\CreateCourseForm.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\AdminTechEvents.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\emailNotifications.ts", ["265"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\techEvents.ts", ["266", "267"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\notifications.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\TechUpdates.tsx", ["268"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\PollsManagement.tsx", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\polls.ts", [], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentPolls.tsx", ["269"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\PollsFormsManagement.tsx", ["270"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentPollsForms.tsx", ["271"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\admin\\TechUpdatesManagement.tsx", ["272", "273", "274", "275", "276", "277", "278", "279"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\techUpdates.ts", ["280", "281", "282"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\TechUpdatesView.tsx", ["283"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\realtimeTechEvents.ts", ["284", "285", "286", "287", "288"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\components\\student\\StudentLibrary.tsx", ["289", "290"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\firebase\\realtimeTimetable.ts", ["291", "292", "293", "294", "295", "296"], [], "B:\\ZERO DAY\\ZeroDay-Innovaid\\beautiful-react-ui\\src\\services\\emailService.ts", [], [], {"ruleId": "297", "severity": 1, "message": "298", "line": 30, "column": 6, "nodeType": "299", "messageId": "300", "endLine": 30, "endColumn": 19}, {"ruleId": "297", "severity": 1, "message": "301", "line": 468, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 468, "endColumn": 40}, {"ruleId": "297", "severity": 1, "message": "302", "line": 14, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 14, "endColumn": 24}, {"ruleId": "297", "severity": 1, "message": "303", "line": 84, "column": 7, "nodeType": "299", "messageId": "300", "endLine": 84, "endColumn": 25}, {"ruleId": "304", "severity": 1, "message": "305", "line": 149, "column": 6, "nodeType": "306", "endLine": 149, "endColumn": 24, "suggestions": "307"}, {"ruleId": "297", "severity": 1, "message": "308", "line": 4, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 4, "endColumn": 20}, {"ruleId": "297", "severity": 1, "message": "309", "line": 141, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 141, "endColumn": 40}, {"ruleId": "304", "severity": 1, "message": "310", "line": 44, "column": 6, "nodeType": "306", "endLine": 44, "endColumn": 8, "suggestions": "311"}, {"ruleId": "297", "severity": 1, "message": "312", "line": 26, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 26, "endColumn": 17}, {"ruleId": "297", "severity": 1, "message": "313", "line": 27, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 27, "endColumn": 20}, {"ruleId": "297", "severity": 1, "message": "314", "line": 28, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 28, "endColumn": 17}, {"ruleId": "304", "severity": 1, "message": "315", "line": 42, "column": 6, "nodeType": "306", "endLine": 42, "endColumn": 8, "suggestions": "316"}, {"ruleId": "297", "severity": 1, "message": "317", "line": 60, "column": 26, "nodeType": "299", "messageId": "300", "endLine": 60, "endColumn": 43}, {"ruleId": "297", "severity": 1, "message": "318", "line": 34, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 34, "endColumn": 30}, {"ruleId": "297", "severity": 1, "message": "319", "line": 35, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 35, "endColumn": 24}, {"ruleId": "304", "severity": 1, "message": "320", "line": 153, "column": 6, "nodeType": "306", "endLine": 153, "endColumn": 8, "suggestions": "321"}, {"ruleId": "297", "severity": 1, "message": "322", "line": 285, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 285, "endColumn": 38}, {"ruleId": "297", "severity": 1, "message": "323", "line": 286, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 286, "endColumn": 32}, {"ruleId": "297", "severity": 1, "message": "324", "line": 23, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 23, "endColumn": 24}, {"ruleId": "297", "severity": 1, "message": "312", "line": 24, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 24, "endColumn": 17}, {"ruleId": "297", "severity": 1, "message": "325", "line": 25, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 25, "endColumn": 20}, {"ruleId": "297", "severity": 1, "message": "314", "line": 26, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 26, "endColumn": 17}, {"ruleId": "297", "severity": 1, "message": "326", "line": 27, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 27, "endColumn": 19}, {"ruleId": "297", "severity": 1, "message": "327", "line": 27, "column": 21, "nodeType": "299", "messageId": "300", "endLine": 27, "endColumn": 33}, {"ruleId": "297", "severity": 1, "message": "328", "line": 32, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 32, "endColumn": 31}, {"ruleId": "297", "severity": 1, "message": "329", "line": 32, "column": 33, "nodeType": "299", "messageId": "300", "endLine": 32, "endColumn": 57}, {"ruleId": "297", "severity": 1, "message": "330", "line": 111, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 111, "endColumn": 35}, {"ruleId": "297", "severity": 1, "message": "331", "line": 117, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 117, "endColumn": 34}, {"ruleId": "297", "severity": 1, "message": "332", "line": 256, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 256, "endColumn": 27}, {"ruleId": "297", "severity": 1, "message": "333", "line": 271, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 271, "endColumn": 29}, {"ruleId": "297", "severity": 1, "message": "334", "line": 277, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 277, "endColumn": 30}, {"ruleId": "297", "severity": 1, "message": "335", "line": 2, "column": 56, "nodeType": "299", "messageId": "300", "endLine": 2, "endColumn": 63}, {"ruleId": "297", "severity": 1, "message": "336", "line": 2, "column": 65, "nodeType": "299", "messageId": "300", "endLine": 2, "endColumn": 68}, {"ruleId": "297", "severity": 1, "message": "337", "line": 4, "column": 17, "nodeType": "299", "messageId": "300", "endLine": 4, "endColumn": 27}, {"ruleId": "297", "severity": 1, "message": "338", "line": 4, "column": 29, "nodeType": "299", "messageId": "300", "endLine": 4, "endColumn": 40}, {"ruleId": "297", "severity": 1, "message": "339", "line": 4, "column": 42, "nodeType": "299", "messageId": "300", "endLine": 4, "endColumn": 56}, {"ruleId": "297", "severity": 1, "message": "340", "line": 6, "column": 22, "nodeType": "299", "messageId": "300", "endLine": 6, "endColumn": 29}, {"ruleId": "304", "severity": 1, "message": "341", "line": 51, "column": 6, "nodeType": "306", "endLine": 51, "endColumn": 23, "suggestions": "342"}, {"ruleId": "297", "severity": 1, "message": "336", "line": 2, "column": 65, "nodeType": "299", "messageId": "300", "endLine": 2, "endColumn": 68}, {"ruleId": "297", "severity": 1, "message": "336", "line": 2, "column": 65, "nodeType": "299", "messageId": "300", "endLine": 2, "endColumn": 68}, {"ruleId": "304", "severity": 1, "message": "310", "line": 38, "column": 6, "nodeType": "306", "endLine": 38, "endColumn": 8, "suggestions": "343"}, {"ruleId": "297", "severity": 1, "message": "335", "line": 2, "column": 56, "nodeType": "299", "messageId": "300", "endLine": 2, "endColumn": 63}, {"ruleId": "297", "severity": 1, "message": "336", "line": 2, "column": 65, "nodeType": "299", "messageId": "300", "endLine": 2, "endColumn": 68}, {"ruleId": "344", "severity": 1, "message": "345", "line": 259, "column": 28, "nodeType": "346", "messageId": "347", "endLine": 266, "endColumn": 8}, {"ruleId": "297", "severity": 1, "message": "348", "line": 8, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 8, "endColumn": 12}, {"ruleId": "297", "severity": 1, "message": "349", "line": 18, "column": 33, "nodeType": "299", "messageId": "300", "endLine": 18, "endColumn": 58}, {"ruleId": "304", "severity": 1, "message": "350", "line": 42, "column": 6, "nodeType": "306", "endLine": 42, "endColumn": 40, "suggestions": "351"}, {"ruleId": "304", "severity": 1, "message": "352", "line": 39, "column": 6, "nodeType": "306", "endLine": 39, "endColumn": 8, "suggestions": "353"}, {"ruleId": "304", "severity": 1, "message": "310", "line": 67, "column": 6, "nodeType": "306", "endLine": 67, "endColumn": 17, "suggestions": "354"}, {"ruleId": "304", "severity": 1, "message": "310", "line": 49, "column": 6, "nodeType": "306", "endLine": 49, "endColumn": 17, "suggestions": "355"}, {"ruleId": "297", "severity": 1, "message": "356", "line": 37, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 37, "endColumn": 24}, {"ruleId": "297", "severity": 1, "message": "357", "line": 39, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 39, "endColumn": 20}, {"ruleId": "297", "severity": 1, "message": "358", "line": 39, "column": 22, "nodeType": "299", "messageId": "300", "endLine": 39, "endColumn": 35}, {"ruleId": "297", "severity": 1, "message": "359", "line": 40, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 40, "endColumn": 24}, {"ruleId": "297", "severity": 1, "message": "360", "line": 40, "column": 26, "nodeType": "299", "messageId": "300", "endLine": 40, "endColumn": 43}, {"ruleId": "297", "severity": 1, "message": "361", "line": 41, "column": 10, "nodeType": "299", "messageId": "300", "endLine": 41, "endColumn": 24}, {"ruleId": "297", "severity": 1, "message": "362", "line": 41, "column": 26, "nodeType": "299", "messageId": "300", "endLine": 41, "endColumn": 43}, {"ruleId": "297", "severity": 1, "message": "363", "line": 250, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 250, "endColumn": 24}, {"ruleId": "297", "severity": 1, "message": "364", "line": 13, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 13, "endColumn": 13}, {"ruleId": "297", "severity": 1, "message": "365", "line": 14, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 14, "endColumn": 12}, {"ruleId": "297", "severity": 1, "message": "366", "line": 281, "column": 11, "nodeType": "299", "messageId": "300", "endLine": 281, "endColumn": 64}, {"ruleId": "304", "severity": 1, "message": "310", "line": 28, "column": 6, "nodeType": "306", "endLine": 28, "endColumn": 8, "suggestions": "367"}, {"ruleId": "297", "severity": 1, "message": "368", "line": 10, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 10, "endColumn": 18}, {"ruleId": "297", "severity": 1, "message": "369", "line": 11, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 11, "endColumn": 8}, {"ruleId": "297", "severity": 1, "message": "370", "line": 12, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 12, "endColumn": 15}, {"ruleId": "297", "severity": 1, "message": "371", "line": 13, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 13, "endColumn": 10}, {"ruleId": "297", "severity": 1, "message": "372", "line": 134, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 134, "endColumn": 20}, {"ruleId": "304", "severity": 1, "message": "373", "line": 131, "column": 6, "nodeType": "306", "endLine": 131, "endColumn": 8, "suggestions": "374"}, {"ruleId": "304", "severity": 1, "message": "375", "line": 135, "column": 6, "nodeType": "306", "endLine": 135, "endColumn": 84, "suggestions": "376"}, {"ruleId": "297", "severity": 1, "message": "368", "line": 10, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 10, "endColumn": 18}, {"ruleId": "297", "severity": 1, "message": "369", "line": 11, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 11, "endColumn": 8}, {"ruleId": "297", "severity": 1, "message": "370", "line": 12, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 12, "endColumn": 15}, {"ruleId": "297", "severity": 1, "message": "371", "line": 13, "column": 3, "nodeType": "299", "messageId": "300", "endLine": 13, "endColumn": 10}, {"ruleId": "297", "severity": 1, "message": "372", "line": 176, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 176, "endColumn": 20}, {"ruleId": "297", "severity": 1, "message": "372", "line": 224, "column": 9, "nodeType": "299", "messageId": "300", "endLine": 224, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'LoginFormData' is defined but never used.", "Identifier", "unusedVar", "'titleStyle' is assigned a value but never used.", "'getAllStudents' is defined but never used.", "'mockLostFoundItems' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'cleanupStudentsListener'. Either include it or remove the dependency array.", "ArrayExpression", ["377"], "'getTimetableSlots' is defined but never used.", "'inputStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["378"], "'loading' is assigned a value but never used.", "'submitting' is assigned a value but never used.", "'message' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadComplaints'. Either include it or remove the dependency array.", ["379"], "'setSuccessMessage' is assigned a value but never used.", "'availableDepartments' is assigned a value but never used.", "'availableYears' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'createMockData'. Either include it or remove the dependency array.", ["380"], "'availableDepartmentsForFilter' is assigned a value but never used.", "'availableYearsForFilter' is assigned a value but never used.", "'lostFoundItems' is assigned a value but never used.", "'processing' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "'selectedLostFoundItem' is assigned a value but never used.", "'setSelectedLostFoundItem' is assigned a value but never used.", "'handleApproveLostFoundItem' is assigned a value but never used.", "'handleRejectLostFoundItem' is assigned a value but never used.", "'handleStatusChange' is assigned a value but never used.", "'handlePriorityChange' is assigned a value but never used.", "'handleDeleteComplaint' is assigned a value but never used.", "'onValue' is defined but never used.", "'off' is defined but never used.", "'storageRef' is defined but never used.", "'uploadBytes' is defined but never used.", "'getDownloadURL' is defined but never used.", "'storage' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadRegistrations'. Either include it or remove the dependency array.", ["381"], ["382"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'successCount', 'failedCount'.", "ArrowFunctionExpression", "unsafeRefs", "'deleteDoc' is defined but never used.", "'sendCompleteNotifications' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterEvents'. Either include it or remove the dependency array.", ["383"], "React Hook useEffect has a missing dependency: 'loadPolls'. Either include it or remove the dependency array.", ["384"], ["385"], ["386"], "'showCreateForm' is assigned a value but never used.", "'searchTerm' is assigned a value but never used.", "'setSearchTerm' is assigned a value but never used.", "'filterCategory' is assigned a value but never used.", "'setFilterCategory' is assigned a value but never used.", "'filterPriority' is assigned a value but never used.", "'setFilterPriority' is assigned a value but never used.", "'inactiveUpdates' is assigned a value but never used.", "'onSnapshot' is defined but never used.", "'Timestamp' is defined but never used.", "'notification' is assigned a value but never used.", ["387"], "'serverTimestamp' is defined but never used.", "'query' is defined but never used.", "'orderByChild' is defined but never used.", "'equalTo' is defined but never used.", "'unsubscribe' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'additionalBooks' and 'mockBooks'. Either include them or remove the dependency array.", ["388"], "React Hook useEffect has a missing dependency: 'filterBooks'. Either include it or remove the dependency array.", ["389"], {"desc": "390", "fix": "391"}, {"desc": "392", "fix": "393"}, {"desc": "394", "fix": "395"}, {"desc": "396", "fix": "397"}, {"desc": "398", "fix": "399"}, {"desc": "392", "fix": "400"}, {"desc": "401", "fix": "402"}, {"desc": "403", "fix": "404"}, {"desc": "405", "fix": "406"}, {"desc": "405", "fix": "407"}, {"desc": "392", "fix": "408"}, {"desc": "409", "fix": "410"}, {"desc": "411", "fix": "412"}, "Update the dependencies array to be: [cleanupStudentsListener, studentsListener]", {"range": "413", "text": "414"}, "Update the dependencies array to be: [loadData]", {"range": "415", "text": "416"}, "Update the dependencies array to be: [loadComplaints]", {"range": "417", "text": "418"}, "Update the dependencies array to be: [createMockData]", {"range": "419", "text": "420"}, "Update the dependencies array to be: [loadRegistrations, selectedEventId]", {"range": "421", "text": "422"}, {"range": "423", "text": "416"}, "Update the dependencies array to be: [events, selectedType, searchTerm, filterEvents]", {"range": "424", "text": "425"}, "Update the dependencies array to be: [loadPolls]", {"range": "426", "text": "427"}, "Update the dependencies array to be: [activeTab, loadData]", {"range": "428", "text": "429"}, {"range": "430", "text": "429"}, {"range": "431", "text": "416"}, "Update the dependencies array to be: [additionalBooks, mockBooks]", {"range": "432", "text": "433"}, "Update the dependencies array to be: [searchQuery, selectedDepartment, selectedCategory, availabilityFilter, books, filterBooks]", {"range": "434", "text": "435"}, [6215, 6233], "[cleanupStudentsListener, studentsListener]", [1441, 1443], "[loadData]", [1269, 1271], "[loadComplaints]", [8704, 8706], "[createMockData]", [1442, 1459], "[loadRegistrations, selectedEventId]", [1382, 1384], [1641, 1675], "[events, selectedType, searchTerm, filterEvents]", [1047, 1049], "[loadPolls]", [1657, 1668], "[activeTab, loadData]", [1512, 1523], [952, 954], [22487, 22489], "[additionalBooks, mockBooks]", [22541, 22619], "[searchQuery, selectedDepartment, selectedCategory, availabilityFilter, books, filterBooks]"]