{"ast": null, "code": "import { store } from '../../store/store';\n/**\n * Initiation\n * @param {string} userID - set the EmailJS user ID\n * @param {string} origin - set the EmailJS origin\n */\nexport const init = (userID, origin = 'https://api.emailjs.com') => {\n  store._userID = userID;\n  store._origin = origin;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}