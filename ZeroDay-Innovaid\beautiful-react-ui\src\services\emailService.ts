// Email service for sending <PERSON><PERSON> using EmailJS
import emailjs from 'emailjs-com';

export interface EmailOTPRequest {
  email: string;
  otp: string;
  name?: string;
  expiryTime: string;
}

// EmailJS Configuration
const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_innovaid',
  TEMPLATE_ID: 'template_otp',
  PUBLIC_KEY: 'YOUR_PUBLIC_KEY' // Replace with your EmailJS public key
};

export const sendOTPEmail = async (request: EmailOTPRequest): Promise<boolean> => {
  try {
    console.log('📧 Preparing to send OTP Email to:', request.email);

    // Always show OTP in console for development/testing
    console.log('🔐 YOUR OTP CODE:', request.otp);
    console.log('⏰ Expires:', request.expiryTime);
    console.log('📋 Copy this OTP to complete registration');

    // Create email content
    const emailSubject = `InnovAid SECE - Your OTP Code: ${request.otp}`;
    const emailBody = `
Hello ${request.name || 'User'},

Your OTP for InnovAid SECE registration is: ${request.otp}

This code will expire at: ${request.expiryTime}

Please enter this code in the registration form to complete your account setup.

If you didn't request this, please ignore this email.

Best regards,
InnovAid Team
Sri Eshwar College of Engineering
    `.trim();

    // Try multiple email sending methods

    // Method 1: Try EmailJS if configured
    if (EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY') {
      try {
        const templateParams = {
          to_email: request.email,
          to_name: request.name || 'User',
          otp_code: request.otp,
          expiry_time: request.expiryTime,
          platform_name: 'InnovAid for SECE',
          college_name: 'Sri Eshwar College of Engineering'
        };

        const result = await emailjs.send(
          EMAILJS_CONFIG.SERVICE_ID,
          EMAILJS_CONFIG.TEMPLATE_ID,
          templateParams,
          EMAILJS_CONFIG.PUBLIC_KEY
        );

        if (result.status === 200) {
          console.log('✅ Email sent successfully via EmailJS');
          return true;
        }
      } catch (emailError) {
        console.warn('⚠️ EmailJS failed:', emailError);
      }
    }

    // Method 2: Open default email client (fallback)
    try {
      const mailtoLink = `mailto:${request.email}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

      // Create a temporary link and click it
      const link = document.createElement('a');
      link.href = mailtoLink;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('📧 Email client opened with OTP details');
      console.log('📝 Please send the email manually or copy the OTP from console');
    } catch (mailtoError) {
      console.warn('⚠️ Could not open email client:', mailtoError);
    }

    // Method 3: Show alert with OTP (always works)
    setTimeout(() => {
      alert(`OTP for ${request.email}: ${request.otp}\n\nExpires: ${request.expiryTime}\n\nPlease copy this code and enter it in the registration form.`);
    }, 500);

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('✅ OTP delivery attempted via multiple methods');
    console.log('🔗 To setup real email sending:');
    console.log('1. Sign up at https://emailjs.com');
    console.log('2. Create email service and template');
    console.log('3. Update EMAILJS_CONFIG.PUBLIC_KEY in emailService.ts');

    return true;
  } catch (error) {
    console.error('❌ Error in OTP delivery:', error);
    // Even if email fails, show OTP in alert as fallback
    alert(`OTP for registration: ${request.otp}\n\nExpires: ${request.expiryTime}`);
    return true; // Return true since user can still get OTP from alert
  }
};

export const getEmailTemplate = (otp: string, expiryTime: string): string => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }
        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎓 InnovAid for SECE</h1>
          <p>Email Verification Required</p>
        </div>
        <div class="content">
          <h2>Welcome to InnovAid!</h2>
          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>
          
          <div class="otp-box">
            <p>Your verification code is:</p>
            <div class="otp-code">${otp}</div>
          </div>
          
          <div class="warning">
            <strong>⚠️ Important:</strong>
            <ul>
              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>
              <li>Do not share this code with anyone</li>
              <li>If you didn't request this, please ignore this email</li>
            </ul>
          </div>
          
          <p>Once verified, you'll have access to all campus utilities including:</p>
          <ul>
            <li>📚 Library Management</li>
            <li>🏠 Hostel Services</li>
            <li>📅 Timetable Access</li>
            <li>🚀 Tech Events & Updates</li>
            <li>📋 Polls & Forms</li>
            <li>🔍 Lost & Found</li>
          </ul>
          
          <div class="footer">
            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};
