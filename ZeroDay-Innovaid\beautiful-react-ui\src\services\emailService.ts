// Email service for sending OTP
// This is a mock implementation - in production, integrate with EmailJS or similar service

export interface EmailOTPRequest {
  email: string;
  otp: string;
  name?: string;
  expiryTime: string;
}

export const sendOTPEmail = async (request: EmailOTPRequest): Promise<boolean> => {
  try {
    // Mock email sending - replace with actual email service
    console.log('📧 Sending OTP Email:');
    console.log(`To: ${request.email}`);
    console.log(`OTP: ${request.otp}`);
    console.log(`Expires: ${request.expiryTime}`);
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In production, use EmailJS or similar service:
    /*
    const emailjs = await import('emailjs-com');
    
    const templateParams = {
      to_email: request.email,
      to_name: request.name || 'User',
      otp_code: request.otp,
      expiry_time: request.expiryTime,
      platform_name: 'InnovAid for SECE'
    };

    const result = await emailjs.send(
      'YOUR_SERVICE_ID',
      'YOUR_TEMPLATE_ID',
      templateParams,
      'YOUR_PUBLIC_KEY'
    );

    return result.status === 200;
    */
    
    // Mock success
    return true;
  } catch (error) {
    console.error('Error sending OTP email:', error);
    return false;
  }
};

export const getEmailTemplate = (otp: string, expiryTime: string): string => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }
        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎓 InnovAid for SECE</h1>
          <p>Email Verification Required</p>
        </div>
        <div class="content">
          <h2>Welcome to InnovAid!</h2>
          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>
          
          <div class="otp-box">
            <p>Your verification code is:</p>
            <div class="otp-code">${otp}</div>
          </div>
          
          <div class="warning">
            <strong>⚠️ Important:</strong>
            <ul>
              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>
              <li>Do not share this code with anyone</li>
              <li>If you didn't request this, please ignore this email</li>
            </ul>
          </div>
          
          <p>Once verified, you'll have access to all campus utilities including:</p>
          <ul>
            <li>📚 Library Management</li>
            <li>🏠 Hostel Services</li>
            <li>📅 Timetable Access</li>
            <li>🚀 Tech Events & Updates</li>
            <li>📋 Polls & Forms</li>
            <li>🔍 Lost & Found</li>
          </ul>
          
          <div class="footer">
            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};
