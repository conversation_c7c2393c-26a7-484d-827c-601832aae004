{"ast": null, "code": "import React,{useState}from'react';// @ts-ignore\nimport{registerUser,loginUser}from'../firebase/auth';import{sendOTPEmail}from'../services/emailService';import UserDashboard from'./UserDashboard';import AdminDashboard from'./AdminDashboard';// Union type for form data (used in formData variable)\n// OTP verification state\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SimpleLogin=()=>{const[selectedRole,setSelectedRole]=useState('user');const[isLogin,setIsLogin]=useState(true);// Separate form states for admin and student\nconst[studentFormData,setStudentFormData]=useState({email:'',password:'',role:'user',name:'',studentId:'',department:'',year:undefined,phone:''});const[adminFormData,setAdminFormData]=useState({email:'',password:'',role:'admin',name:'',department:'',phone:''});// Get current form data based on selected role\nconst formData=selectedRole==='user'?studentFormData:adminFormData;const[isLoading,setIsLoading]=useState(false);const[message,setMessage]=useState('');const[currentUser,setCurrentUser]=useState(null);// OTP verification state\nconst[otpState,setOtpState]=useState({isOTPSent:false,isOTPVerified:false,otpCode:'',generatedOTP:'',otpExpiry:0});// Generate 6-digit OTP\nconst generateOTP=()=>{return Math.floor(100000+Math.random()*900000).toString();};// Send OTP via email\nconst sendOTP=async email=>{try{const otp=generateOTP();const expiry=Date.now()+10*60*1000;// 10 minutes expiry\nconst expiryTime=new Date(expiry).toLocaleString();// Send OTP email\nconst emailSent=await sendOTPEmail({email,otp,name:formData.name,expiryTime});if(!emailSent){setMessage('❌ Failed to send OTP email. Please check your email address and try again.');return false;}setOtpState(prev=>({...prev,isOTPSent:true,generatedOTP:otp,otpExpiry:expiry}));setMessage(`📧 OTP sent to ${email}. Please check your email and enter the 6-digit code below. (Check spam folder if not found)`);return true;}catch(error){console.error('Error sending OTP:',error);setMessage('❌ Failed to send OTP. Please try again.');return false;}};// Verify OTP\nconst verifyOTP=()=>{if(!otpState.otpCode||otpState.otpCode.length!==6){setMessage('❌ Please enter a valid 6-digit OTP.');return false;}if(Date.now()>otpState.otpExpiry){setMessage('❌ OTP has expired. Please request a new one.');setOtpState(prev=>({...prev,isOTPSent:false,otpCode:'',generatedOTP:''}));return false;}if(otpState.otpCode!==otpState.generatedOTP){setMessage('❌ Invalid OTP. Please check and try again.');return false;}setOtpState(prev=>({...prev,isOTPVerified:true}));setMessage('✅ OTP verified successfully! Proceeding with registration...');return true;};const handleInputChange=e=>{const{name,value}=e.target;if(selectedRole==='user'){setStudentFormData(prev=>({...prev,[name]:name==='year'?parseInt(value):value}));}else{setAdminFormData(prev=>({...prev,[name]:value}));}};// Handle OTP input change\nconst handleOTPChange=e=>{const value=e.target.value.replace(/\\D/g,'').slice(0,6);// Only digits, max 6\nsetOtpState(prev=>({...prev,otpCode:value}));};// Resend OTP\nconst handleResendOTP=async()=>{setOtpState(prev=>({...prev,isOTPSent:false,otpCode:'',generatedOTP:'',otpExpiry:0}));await sendOTP(formData.email);};const handleSubmit=async e=>{e.preventDefault();setIsLoading(true);setMessage('');try{if(isLogin){// Login with Firebase\nconst result=await loginUser(formData.email,formData.password);if(result.success&&result.user){// Validate that the selected role matches the user's actual role\nconst userRole=result.user.role;const selectedUserRole=selectedRole==='user'?'user':'admin';if(userRole!==selectedUserRole){const correctRoleText=userRole==='user'?'Student':'Admin';const selectedRoleText=selectedUserRole==='user'?'Student':'Admin';setMessage(`Role mismatch: You are registered as a ${correctRoleText}, but trying to login as ${selectedRoleText}. Please select the correct role above.`);return;}setMessage(`Welcome back, ${result.user.name}! Login successful.`);localStorage.setItem('user',JSON.stringify(result.user));setCurrentUser(result.user);}else{setMessage(result.message);}}else{// Register with Firebase - Multi-step process with OTP\nif(!formData.name||formData.name.trim()===''){setMessage('Please enter your full name to complete registration.');return;}// Validate email domain for institutional email\nif(selectedRole==='user'){// Student email validation\nif(!formData.email.endsWith('@sece.ac.in')){setMessage('Please use your institutional email address ending with @sece.ac.in');return;}}else{// Admin email validation\nif(!formData.email.endsWith('<EMAIL>')){setMessage('Admin registration requires email <NAME_EMAIL>');return;}}// Step 1: Send OTP if not sent yet\nif(!otpState.isOTPSent){const otpSent=await sendOTP(formData.email);if(!otpSent){return;}setIsLoading(false);return;}// Step 2: Verify OTP if sent but not verified\nif(otpState.isOTPSent&&!otpState.isOTPVerified){const isOTPValid=verifyOTP();if(!isOTPValid){setIsLoading(false);return;}// Continue to registration after OTP verification\n}// Handle different form types for registration\nlet userData;if(selectedRole==='user'){const studentData=formData;userData={name:studentData.name,role:'user',studentId:studentData.studentId,department:studentData.department,year:studentData.year,phone:studentData.phone};}else{const adminData=formData;userData={name:adminData.name,role:'admin',department:adminData.department,phone:adminData.phone};}const result=await registerUser(formData.email,formData.password,userData);if(result.success&&result.user){setMessage(`Registration successful! Welcome to the platform, ${result.user.name}!`);localStorage.setItem('user',JSON.stringify(result.user));setCurrentUser(result.user);}else{setMessage(result.message);}}}catch(error){console.error('Authentication error:',error);setMessage('Something went wrong. Please check your connection and try again.');}finally{setIsLoading(false);}};const handleLogout=async()=>{try{// @ts-ignore\nconst{logoutUser}=await import('../firebase/auth');const result=await logoutUser();if(result.success){localStorage.removeItem('user');setCurrentUser(null);setMessage('You have been logged out successfully.');// Reset both form states\nsetStudentFormData({email:'',password:'',role:'user',name:'',studentId:'',department:'',year:undefined,phone:''});setAdminFormData({email:'',password:'',role:'admin',name:'',department:'',phone:''});}else{setMessage(`Logout failed: ${result.message}`);}}catch(error){console.error('Logout error:',error);// Fallback logout\nlocalStorage.removeItem('user');setCurrentUser(null);setMessage('You have been logged out successfully.');// Reset both form states\nsetStudentFormData({email:'',password:'',role:'user',name:'',studentId:'',department:'',year:undefined,phone:''});setAdminFormData({email:'',password:'',role:'admin',name:'',department:'',phone:''});}};// TEMPORARILY DISABLED AUTO-LOGIN FOR TESTING LOGIN VALIDATION\n// Check if user is logged in on component mount and set up Firebase auth listener\nReact.useEffect(()=>{// Clear any existing session to test login properly\nlocalStorage.removeItem('user');setCurrentUser(null);// Commented out auto-login for testing\n/*\r\n    const setupAuthListener = async () => {\r\n      try {\r\n        // @ts-ignore\r\n        const { auth } = await import('../firebase/config');\r\n        // @ts-ignore\r\n        const { onAuthStateChanged } = await import('firebase/auth');\r\n        // @ts-ignore\r\n        const { getCurrentUserData } = await import('../firebase/auth');\r\n\r\n        const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\r\n          try {\r\n            if (firebaseUser) {\r\n              // User is signed in\r\n              console.log('🔐 Firebase user authenticated:', firebaseUser.uid);\r\n              const userData = await getCurrentUserData(firebaseUser);\r\n              if (userData) {\r\n                console.log('✅ User data loaded:', userData.name);\r\n                setCurrentUser(userData);\r\n                localStorage.setItem('user', JSON.stringify(userData));\r\n              } else {\r\n                console.warn('⚠️ No user data found for authenticated user');\r\n              }\r\n            } else {\r\n              // User is signed out\r\n              console.log('🔓 User signed out');\r\n              setCurrentUser(null);\r\n              localStorage.removeItem('user');\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Error in auth state change handler:', error);\r\n            // Don't set user state if there's an error\r\n            setCurrentUser(null);\r\n            localStorage.removeItem('user');\r\n          }\r\n        }, (error) => {\r\n          console.error('❌ Firebase auth state change error:', error);\r\n          setCurrentUser(null);\r\n          localStorage.removeItem('user');\r\n        });\r\n\r\n        return unsubscribe;\r\n      } catch (error) {\r\n        console.error('Error setting up auth listener:', error);\r\n        // Fallback to localStorage check\r\n        const userData = localStorage.getItem('user');\r\n        if (userData) {\r\n          try {\r\n            const user = JSON.parse(userData);\r\n            setCurrentUser(user);\r\n          } catch (error) {\r\n            console.error('Error parsing user data:', error);\r\n            localStorage.removeItem('user');\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    setupAuthListener();\r\n    */},[]);// If user is logged in, show appropriate dashboard\nif(currentUser){if(currentUser.role==='admin'){return/*#__PURE__*/_jsx(AdminDashboard,{user:currentUser,onLogout:handleLogout});}else{return/*#__PURE__*/_jsx(UserDashboard,{user:currentUser,onLogout:handleLogout});}}const containerStyle={minHeight:'100vh',height:'100vh',background:`\n      linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)),\n      url('/college-building.jpg')\n    `,backgroundSize:'cover',backgroundPosition:'center',backgroundRepeat:'no-repeat',backgroundAttachment:'fixed',display:'flex',alignItems:'center',justifyContent:'center',fontFamily:'Arial, sans-serif',padding:'2rem',overflow:'hidden'};const cardStyle={background:'rgba(255, 255, 255, 0.85)',backdropFilter:'blur(15px)',borderRadius:'25px',padding:'2.5rem',boxShadow:'0 25px 50px rgba(0,0,0,0.15)',border:'1px solid rgba(255, 255, 255, 0.2)',maxWidth:'500px',width:'100%',position:'relative',zIndex:1};const headerStyle={textAlign:'center',marginBottom:'2rem'};const titleStyle={fontSize:'2.5rem',fontWeight:'800',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',marginBottom:'0.5rem'};const roleButtonStyle=active=>({flex:1,padding:'1rem',border:active?'2px solid #667eea':'2px solid #ddd',borderRadius:'10px',background:active?'#667eea':'white',color:active?'white':'#666',cursor:'pointer',fontWeight:'600',margin:'0 0.5rem'});const inputStyle={width:'100%',padding:'1rem',border:'2px solid #ddd',borderRadius:'10px',fontSize:'1rem',marginBottom:'1rem'};const buttonStyle={width:'100%',padding:'1rem',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',color:'white',border:'none',borderRadius:'10px',fontSize:'1.1rem',fontWeight:'600',cursor:isLoading?'not-allowed':'pointer',opacity:isLoading?0.7:1};const isSuccessMessage=message.includes('successful')||message.includes('Welcome')||message.includes('logged out');const isErrorMessage=message.includes('failed')||message.includes('error')||message.includes('Error')||message.includes('Invalid')||message.includes('incorrect')||message.includes('mismatch')||message.includes('required')||message.includes('Please')||message.includes('No account')||message.includes('disabled')||message.includes('Too many')||message.includes('Network');const messageStyle={padding:'1rem',borderRadius:'10px',marginTop:'1rem',background:isSuccessMessage?'#d4edda':isErrorMessage?'#f8d7da':'#e2e3e5',color:isSuccessMessage?'#155724':isErrorMessage?'#721c24':'#383d41',border:`1px solid ${isSuccessMessage?'#c3e6cb':isErrorMessage?'#f5c6cb':'#d6d8db'}`,fontSize:'0.9rem',textAlign:'center',fontWeight:'500'};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"style\",{children:`\n          body {\n            margin: 0;\n            padding: 0;\n            overflow-x: hidden;\n          }\n\n          /* Fallback background in case image doesn't load */\n          .login-container {\n            background-image:\n              linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)),\n              url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\"><defs><linearGradient id=\"sky\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\"><stop offset=\"0%\" style=\"stop-color:%2387CEEB;stop-opacity:1\" /><stop offset=\"100%\" style=\"stop-color:%23B0E0E6;stop-opacity:1\" /></linearGradient></defs><rect fill=\"url(%23sky)\" width=\"1200\" height=\"600\"/><rect fill=\"%2398FB98\" y=\"600\" width=\"1200\" height=\"200\"/><rect fill=\"%23F0E68C\" x=\"50\" y=\"200\" width=\"280\" height=\"400\" rx=\"10\"/><rect fill=\"%23E6E6FA\" x=\"350\" y=\"150\" width=\"350\" height=\"450\" rx=\"10\"/><rect fill=\"%23F0E68C\" x=\"720\" y=\"180\" width=\"250\" height=\"420\" rx=\"10\"/><rect fill=\"%23DDA0DD\" x=\"990\" y=\"220\" width=\"180\" height=\"380\" rx=\"10\"/><circle fill=\"%23228B22\" cx=\"150\" cy=\"650\" r=\"40\"/><circle fill=\"%23228B22\" cx=\"400\" cy=\"670\" r=\"35\"/><circle fill=\"%23228B22\" cx=\"700\" cy=\"660\" r=\"45\"/><circle fill=\"%23228B22\" cx=\"1000\" cy=\"650\" r=\"38\"/></svg>');\n          }\n        `}),/*#__PURE__*/_jsx(\"div\",{style:containerStyle,className:\"login-container\",children:/*#__PURE__*/_jsxs(\"div\",{style:cardStyle,children:[/*#__PURE__*/_jsxs(\"div\",{style:headerStyle,children:[/*#__PURE__*/_jsx(\"div\",{style:{margin:'0 auto 1rem',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(\"img\",{src:\"/logo-light.png\",alt:\"InnovAid Logo\",style:{height:'100px',width:'auto',objectFit:'contain'}})}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#666',fontSize:'1.1rem'},children:\"Innovaid for SECE - Campus Utilities Platform\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',marginBottom:'2rem'},children:[/*#__PURE__*/_jsx(\"button\",{style:roleButtonStyle(selectedRole==='user'),onClick:()=>{setSelectedRole('user');// Don't copy data between roles - keep forms completely separate\n},children:\"\\uD83D\\uDC68\\u200D\\uD83C\\uDF93 Student\"}),/*#__PURE__*/_jsx(\"button\",{style:roleButtonStyle(selectedRole==='admin'),onClick:()=>{setSelectedRole('admin');// Don't copy data between roles - keep forms completely separate\n},children:\"\\uD83D\\uDEE1\\uFE0F Admin\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[!isLogin&&/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"text\",name:\"name\",placeholder:\"Full Name\",value:formData.name||'',onChange:handleInputChange,required:true}),/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"email\",name:\"email\",placeholder:\"Email Address\",value:formData.email,onChange:handleInputChange,required:true}),/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"password\",name:\"password\",placeholder:\"Password\",value:formData.password,onChange:handleInputChange,required:true}),!isLogin&&otpState.isOTPSent&&!otpState.isOTPVerified&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'1rem'},children:[/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"text\",placeholder:\"Enter 6-digit OTP\",value:otpState.otpCode,onChange:handleOTPChange,maxLength:6,required:true}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginTop:'0.5rem',fontSize:'0.9rem'},children:[/*#__PURE__*/_jsxs(\"span\",{style:{color:'#666'},children:[\"OTP expires in: \",Math.max(0,Math.ceil((otpState.otpExpiry-Date.now())/60000)),\" min\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:handleResendOTP,style:{background:'none',border:'none',color:'#667eea',cursor:'pointer',textDecoration:'underline',fontSize:'0.9rem'},children:\"Resend OTP\"})]})]}),!isLogin&&selectedRole==='user'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"text\",name:\"studentId\",placeholder:\"Student ID\",value:formData.studentId||'',onChange:handleInputChange,required:true}),/*#__PURE__*/_jsxs(\"select\",{style:inputStyle,name:\"department\",value:formData.department||'',onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Department\"}),/*#__PURE__*/_jsx(\"option\",{value:\"AIDS\",children:\"AIDS (Artificial Intelligence & Data Science)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CSE\",children:\"CSE (Computer Science & Engineering)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"EEE\",children:\"EEE (Electrical & Electronics Engineering)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"ECE\",children:\"ECE (Electronics & Communication Engineering)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"MECH\",children:\"MECH (Mechanical Engineering)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CSBS\",children:\"CSBS (Computer Science & Business Systems)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"AIML\",children:\"AIML (Artificial Intelligence & Machine Learning)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CYBER\",children:\"CYBER (Cyber Security)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CCE\",children:\"CCE (Computer & Communication Engineering)\"})]}),/*#__PURE__*/_jsxs(\"select\",{style:inputStyle,name:\"year\",value:formData.year||'',onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Year\"}),/*#__PURE__*/_jsx(\"option\",{value:\"1\",children:\"1st Year\"}),/*#__PURE__*/_jsx(\"option\",{value:\"2\",children:\"2nd Year\"}),/*#__PURE__*/_jsx(\"option\",{value:\"3\",children:\"3rd Year\"}),/*#__PURE__*/_jsx(\"option\",{value:\"4\",children:\"4th Year\"})]}),/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"tel\",name:\"phone\",placeholder:\"Phone Number (Optional)\",value:formData.phone||'',onChange:handleInputChange})]}),!isLogin&&selectedRole==='admin'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"text\",name:\"department\",placeholder:\"Department/Office (e.g., Administration)\",value:formData.department||'',onChange:handleInputChange,required:true}),/*#__PURE__*/_jsx(\"input\",{style:inputStyle,type:\"tel\",name:\"phone\",placeholder:\"Phone Number\",value:formData.phone||'',onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",style:buttonStyle,disabled:isLoading,children:isLoading?'⏳ Processing...':isLogin?`🔐 Sign In as ${selectedRole==='user'?'Student':'Admin'}`:!otpState.isOTPSent?`📧 Send OTP`:!otpState.isOTPVerified?`🔐 Verify OTP`:`📝 Complete Registration`})]}),/*#__PURE__*/_jsx(\"button\",{style:{background:'none',border:'none',color:'#666',cursor:'pointer',fontSize:'0.9rem',width:'100%',padding:'1rem',marginTop:'1rem'},onClick:()=>{setIsLogin(!isLogin);setMessage('');// Clear both form states when toggling\nsetStudentFormData({email:'',password:'',role:'user',name:'',studentId:'',department:'',year:undefined,phone:''});setAdminFormData({email:'',password:'',role:'admin',name:'',department:'',phone:''});// Reset OTP state\nsetOtpState({isOTPSent:false,isOTPVerified:false,otpCode:'',generatedOTP:'',otpExpiry:0});},children:isLogin?\"Don't have an account? Register here\":\"Already have an account? Sign in\"}),message&&/*#__PURE__*/_jsx(\"div\",{style:messageStyle,children:message})]})})]});};export default SimpleLogin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}