{"name": "emailjs-com", "version": "3.2.0", "description": "Send emails using predefined templates and your private email account", "private": false, "author": "<PERSON> <<EMAIL>> (https://www.emailjs.com)", "contributors": ["<PERSON> <<EMAIL>> (https://www.emailjs.com)"], "license": "MIT", "homepage": "https://www.emailjs.com", "main": "cjs/index.js", "types": "es/index.d.ts", "module": "es", "repository": {"type": "git", "url": "https://github.com/emailjs-com/emailjs-sdk.git"}, "engines": {"node": ">=12.0.0"}, "scripts": {"_clean": "rm -rf es && rm -rf cjs && rm -rf dist", "_set-version": "./set-version.sh $npm_package_version", "_build-ts": "tsc --declaration && npm run _set-version es", "_build-cjs": "tsc --module commonjs --outDir cjs && npm run _set-version cjs", "_build-bundle": "webpack --env production", "build": "npm run _clean && npm run _build-ts && npm run _build-cjs && npm run _build-bundle", "test": "jest --coverage", "lint": "tsc --noEmit && eslint src"}, "keywords": ["js email", "es6 email", "ts email", "javascript email", "typescript email", "emailjs", "email templates", "send email", "send email from js", "emailjs-com"], "devDependencies": {"@babel/preset-env": "^7.14.7", "@babel/preset-typescript": "^7.14.5", "@types/jest": "^26.0.24", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "babel-jest": "^27.0.6", "babel-loader": "^8.2.2", "core-js": "^3.15.2", "eslint": "^7.30.0", "eslint-import-resolver-typescript": "^2.4.0", "eslint-plugin-import": "^2.23.4", "jest": "^27.0.6", "prettier": "^2.3.2", "terser-webpack-plugin": "^5.1.4", "typescript": "^4.3.5", "webpack": "^5.44.0", "webpack-cli": "^4.7.2"}, "prettier": {"trailingComma": "all", "singleQuote": true, "printWidth": 100}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "current"}}], "@babel/preset-typescript"]}}