// EmailJS Configuration for OTP Emails
// Update these values with your EmailJS credentials

export const EMAIL_CONFIG = {
  // Get these from your EmailJS dashboard: https://dashboard.emailjs.com
  
  // 1. SERVICE_ID: From Email Services page (after connecting Gmail/Outlook)
  SERVICE_ID: 'YOUR_SERVICE_ID',
  
  // 2. TEMPLATE_ID: From Email Templates page (after creating OTP template)
  TEMPLATE_ID: 'YOUR_TEMPLATE_ID',
  
  // 3. PUBLIC_KEY: From Account → API Keys page
  PUBLIC_KEY: 'YOUR_PUBLIC_KEY',
  
  // Email template variables (these match the template you create)
  TEMPLATE_VARS: {
    to_email: '{{to_email}}',
    to_name: '{{to_name}}',
    otp_code: '{{otp_code}}',
    expiry_time: '{{expiry_time}}',
    platform_name: '{{platform_name}}',
    college_name: '{{college_name}}'
  }
};

// Instructions:
// 1. Go to https://dashboard.emailjs.com
// 2. Add Email Service (Gmail recommended)
// 3. Create Email Template with OTP content
// 4. Get your Service ID, Template ID, and Public Key
// 5. Replace the values above
// 6. Test your OTP emails!

export const EMAIL_TEMPLATE_CONTENT = {
  subject: 'InnovAid SECE - Your OTP Code: {{otp_code}}',
  
  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
        <h1>🎓 InnovAid for SECE</h1>
        <p>Email Verification Required</p>
      </div>
      
      <div style="background: #f9f9f9; padding: 30px;">
        <h2>Hello {{to_name}},</h2>
        <p>Thank you for registering with InnovAid for SECE. To complete your registration, please use the OTP below:</p>
        
        <div style="background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0;">
          <p>Your verification code is:</p>
          <div style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px;">{{otp_code}}</div>
        </div>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
          <strong>⚠️ Important:</strong>
          <ul>
            <li>This OTP will expire at: <strong>{{expiry_time}}</strong></li>
            <li>Do not share this code with anyone</li>
            <li>If you didn't request this, please ignore this email</li>
          </ul>
        </div>
        
        <p>Once verified, you'll have access to all campus utilities!</p>
        
        <div style="text-align: center; color: #666; font-size: 14px; margin-top: 20px;">
          <p>Best regards,<br>InnovAid Team<br>{{college_name}}</p>
        </div>
      </div>
    </div>
  `,
  
  text: `
Hello {{to_name}},

Your OTP for InnovAid SECE registration is: {{otp_code}}

This code will expire at: {{expiry_time}}

Please enter this code in the registration form to complete your account setup for {{platform_name}}.

If you didn't request this, please ignore this email.

Best regards,
InnovAid Team
{{college_name}}
  `
};
