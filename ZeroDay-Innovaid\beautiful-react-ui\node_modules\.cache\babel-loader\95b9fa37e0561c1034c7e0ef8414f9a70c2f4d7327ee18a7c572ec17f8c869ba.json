{"ast": null, "code": "var _jsxFileName = \"B:\\\\ZERO DAY\\\\ZeroDay-Innovaid\\\\beautiful-react-ui\\\\src\\\\components\\\\SimpleLogin.tsx\";\nimport React, { useState } from 'react';\n// @ts-ignore\nimport { registerUser, loginUser } from '../firebase/auth';\nimport { sendOTPEmail } from '../services/emailService';\nimport UserDashboard from './UserDashboard';\nimport AdminDashboard from './AdminDashboard';\n\n// Union type for form data (used in formData variable)\n\n// OTP verification state\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleLogin = () => {\n  const [selectedRole, setSelectedRole] = useState('user');\n  const [isLogin, setIsLogin] = useState(true);\n\n  // Separate form states for admin and student\n  const [studentFormData, setStudentFormData] = useState({\n    email: '',\n    password: '',\n    role: 'user',\n    name: '',\n    studentId: '',\n    department: '',\n    year: undefined,\n    phone: ''\n  });\n  const [adminFormData, setAdminFormData] = useState({\n    email: '',\n    password: '',\n    role: 'admin',\n    name: '',\n    department: '',\n    phone: ''\n  });\n\n  // Get current form data based on selected role\n  const formData = selectedRole === 'user' ? studentFormData : adminFormData;\n  const [isLoading, setIsLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [currentUser, setCurrentUser] = useState(null);\n\n  // OTP verification state\n  const [otpState, setOtpState] = useState({\n    isOTPSent: false,\n    isOTPVerified: false,\n    otpCode: '',\n    generatedOTP: '',\n    otpExpiry: 0\n  });\n\n  // Generate 6-digit OTP\n  const generateOTP = () => {\n    return Math.floor(100000 + Math.random() * 900000).toString();\n  };\n\n  // Send OTP via email\n  const sendOTP = async email => {\n    try {\n      const otp = generateOTP();\n      const expiry = Date.now() + 10 * 60 * 1000; // 10 minutes expiry\n      const expiryTime = new Date(expiry).toLocaleString();\n\n      // Send OTP email\n      const emailSent = await sendOTPEmail({\n        email,\n        otp,\n        name: formData.name,\n        expiryTime\n      });\n      if (!emailSent) {\n        setMessage('❌ Failed to send OTP email. Please check your email address and try again.');\n        return false;\n      }\n      setOtpState(prev => ({\n        ...prev,\n        isOTPSent: true,\n        generatedOTP: otp,\n        otpExpiry: expiry\n      }));\n      setMessage(`📧 OTP sent to ${email}. Please check your email and enter the 6-digit code below. (Check spam folder if not found)`);\n      return true;\n    } catch (error) {\n      console.error('Error sending OTP:', error);\n      setMessage('❌ Failed to send OTP. Please try again.');\n      return false;\n    }\n  };\n\n  // Verify OTP\n  const verifyOTP = () => {\n    if (!otpState.otpCode || otpState.otpCode.length !== 6) {\n      setMessage('❌ Please enter a valid 6-digit OTP.');\n      return false;\n    }\n    if (Date.now() > otpState.otpExpiry) {\n      setMessage('❌ OTP has expired. Please request a new one.');\n      setOtpState(prev => ({\n        ...prev,\n        isOTPSent: false,\n        otpCode: '',\n        generatedOTP: ''\n      }));\n      return false;\n    }\n    if (otpState.otpCode !== otpState.generatedOTP) {\n      setMessage('❌ Invalid OTP. Please check and try again.');\n      return false;\n    }\n    setOtpState(prev => ({\n      ...prev,\n      isOTPVerified: true\n    }));\n    setMessage('✅ OTP verified successfully! Proceeding with registration...');\n    return true;\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (selectedRole === 'user') {\n      setStudentFormData(prev => ({\n        ...prev,\n        [name]: name === 'year' ? parseInt(value) : value\n      }));\n    } else {\n      setAdminFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  // Handle OTP input change\n  const handleOTPChange = e => {\n    const value = e.target.value.replace(/\\D/g, '').slice(0, 6); // Only digits, max 6\n    setOtpState(prev => ({\n      ...prev,\n      otpCode: value\n    }));\n  };\n\n  // Resend OTP\n  const handleResendOTP = async () => {\n    setOtpState(prev => ({\n      ...prev,\n      isOTPSent: false,\n      otpCode: '',\n      generatedOTP: '',\n      otpExpiry: 0\n    }));\n    await sendOTP(formData.email);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    setMessage('');\n    try {\n      if (isLogin) {\n        // Login with Firebase\n        const result = await loginUser(formData.email, formData.password);\n        if (result.success && result.user) {\n          // Validate that the selected role matches the user's actual role\n          const userRole = result.user.role;\n          const selectedUserRole = selectedRole === 'user' ? 'user' : 'admin';\n          if (userRole !== selectedUserRole) {\n            const correctRoleText = userRole === 'user' ? 'Student' : 'Admin';\n            const selectedRoleText = selectedUserRole === 'user' ? 'Student' : 'Admin';\n            setMessage(`Role mismatch: You are registered as a ${correctRoleText}, but trying to login as ${selectedRoleText}. Please select the correct role above.`);\n            return;\n          }\n          setMessage(`Welcome back, ${result.user.name}! Login successful.`);\n          localStorage.setItem('user', JSON.stringify(result.user));\n          setCurrentUser(result.user);\n        } else {\n          setMessage(result.message);\n        }\n      } else {\n        // Register with Firebase - Multi-step process with OTP\n        if (!formData.name || formData.name.trim() === '') {\n          setMessage('Please enter your full name to complete registration.');\n          return;\n        }\n\n        // Validate email domain for institutional email\n        if (selectedRole === 'user') {\n          // Student email validation\n          if (!formData.email.endsWith('@sece.ac.in')) {\n            setMessage('Please use your institutional email address ending with @sece.ac.in');\n            return;\n          }\n        } else {\n          // Admin email validation\n          if (!formData.email.endsWith('<EMAIL>')) {\n            setMessage('Admin registration requires email <NAME_EMAIL>');\n            return;\n          }\n        }\n\n        // Step 1: Send OTP if not sent yet\n        if (!otpState.isOTPSent) {\n          const otpSent = await sendOTP(formData.email);\n          if (!otpSent) {\n            return;\n          }\n          setIsLoading(false);\n          return;\n        }\n\n        // Step 2: Verify OTP if sent but not verified\n        if (otpState.isOTPSent && !otpState.isOTPVerified) {\n          const isOTPValid = verifyOTP();\n          if (!isOTPValid) {\n            setIsLoading(false);\n            return;\n          }\n          // Continue to registration after OTP verification\n        }\n\n        // Handle different form types for registration\n        let userData;\n        if (selectedRole === 'user') {\n          const studentData = formData;\n          userData = {\n            name: studentData.name,\n            role: 'user',\n            studentId: studentData.studentId,\n            department: studentData.department,\n            year: studentData.year,\n            phone: studentData.phone\n          };\n        } else {\n          const adminData = formData;\n          userData = {\n            name: adminData.name,\n            role: 'admin',\n            department: adminData.department,\n            phone: adminData.phone\n          };\n        }\n        const result = await registerUser(formData.email, formData.password, userData);\n        if (result.success && result.user) {\n          setMessage(`Registration successful! Welcome to the platform, ${result.user.name}!`);\n          localStorage.setItem('user', JSON.stringify(result.user));\n          setCurrentUser(result.user);\n        } else {\n          setMessage(result.message);\n        }\n      }\n    } catch (error) {\n      console.error('Authentication error:', error);\n      setMessage('Something went wrong. Please check your connection and try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleLogout = async () => {\n    try {\n      // @ts-ignore\n      const {\n        logoutUser\n      } = await import('../firebase/auth');\n      const result = await logoutUser();\n      if (result.success) {\n        localStorage.removeItem('user');\n        setCurrentUser(null);\n        setMessage('You have been logged out successfully.');\n        // Reset both form states\n        setStudentFormData({\n          email: '',\n          password: '',\n          role: 'user',\n          name: '',\n          studentId: '',\n          department: '',\n          year: undefined,\n          phone: ''\n        });\n        setAdminFormData({\n          email: '',\n          password: '',\n          role: 'admin',\n          name: '',\n          department: '',\n          phone: ''\n        });\n      } else {\n        setMessage(`Logout failed: ${result.message}`);\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Fallback logout\n      localStorage.removeItem('user');\n      setCurrentUser(null);\n      setMessage('You have been logged out successfully.');\n      // Reset both form states\n      setStudentFormData({\n        email: '',\n        password: '',\n        role: 'user',\n        name: '',\n        studentId: '',\n        department: '',\n        year: undefined,\n        phone: ''\n      });\n      setAdminFormData({\n        email: '',\n        password: '',\n        role: 'admin',\n        name: '',\n        department: '',\n        phone: ''\n      });\n    }\n  };\n\n  // TEMPORARILY DISABLED AUTO-LOGIN FOR TESTING LOGIN VALIDATION\n  // Check if user is logged in on component mount and set up Firebase auth listener\n  React.useEffect(() => {\n    // Clear any existing session to test login properly\n    localStorage.removeItem('user');\n    setCurrentUser(null);\n\n    // Commented out auto-login for testing\n    /*\r\n    const setupAuthListener = async () => {\r\n      try {\r\n        // @ts-ignore\r\n        const { auth } = await import('../firebase/config');\r\n        // @ts-ignore\r\n        const { onAuthStateChanged } = await import('firebase/auth');\r\n        // @ts-ignore\r\n        const { getCurrentUserData } = await import('../firebase/auth');\r\n          const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\r\n          try {\r\n            if (firebaseUser) {\r\n              // User is signed in\r\n              console.log('🔐 Firebase user authenticated:', firebaseUser.uid);\r\n              const userData = await getCurrentUserData(firebaseUser);\r\n              if (userData) {\r\n                console.log('✅ User data loaded:', userData.name);\r\n                setCurrentUser(userData);\r\n                localStorage.setItem('user', JSON.stringify(userData));\r\n              } else {\r\n                console.warn('⚠️ No user data found for authenticated user');\r\n              }\r\n            } else {\r\n              // User is signed out\r\n              console.log('🔓 User signed out');\r\n              setCurrentUser(null);\r\n              localStorage.removeItem('user');\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Error in auth state change handler:', error);\r\n            // Don't set user state if there's an error\r\n            setCurrentUser(null);\r\n            localStorage.removeItem('user');\r\n          }\r\n        }, (error) => {\r\n          console.error('❌ Firebase auth state change error:', error);\r\n          setCurrentUser(null);\r\n          localStorage.removeItem('user');\r\n        });\r\n          return unsubscribe;\r\n      } catch (error) {\r\n        console.error('Error setting up auth listener:', error);\r\n        // Fallback to localStorage check\r\n        const userData = localStorage.getItem('user');\r\n        if (userData) {\r\n          try {\r\n            const user = JSON.parse(userData);\r\n            setCurrentUser(user);\r\n          } catch (error) {\r\n            console.error('Error parsing user data:', error);\r\n            localStorage.removeItem('user');\r\n          }\r\n        }\r\n      }\r\n    };\r\n      setupAuthListener();\r\n    */\n  }, []);\n\n  // If user is logged in, show appropriate dashboard\n  if (currentUser) {\n    if (currentUser.role === 'admin') {\n      return /*#__PURE__*/_jsxDEV(AdminDashboard, {\n        user: currentUser,\n        onLogout: handleLogout\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 14\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(UserDashboard, {\n        user: currentUser,\n        onLogout: handleLogout\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 14\n      }, this);\n    }\n  }\n  const containerStyle = {\n    minHeight: '100vh',\n    height: '100vh',\n    background: `\n      linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)),\n      url('/college-building.jpg')\n    `,\n    backgroundSize: 'cover',\n    backgroundPosition: 'center',\n    backgroundRepeat: 'no-repeat',\n    backgroundAttachment: 'fixed',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontFamily: 'Arial, sans-serif',\n    padding: '2rem',\n    overflow: 'hidden'\n  };\n  const cardStyle = {\n    background: 'rgba(255, 255, 255, 0.85)',\n    backdropFilter: 'blur(15px)',\n    borderRadius: '25px',\n    padding: '2.5rem',\n    boxShadow: '0 25px 50px rgba(0,0,0,0.15)',\n    border: '1px solid rgba(255, 255, 255, 0.2)',\n    maxWidth: '500px',\n    width: '100%',\n    position: 'relative',\n    zIndex: 1\n  };\n  const headerStyle = {\n    textAlign: 'center',\n    marginBottom: '2rem'\n  };\n  const titleStyle = {\n    fontSize: '2.5rem',\n    fontWeight: '800',\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    marginBottom: '0.5rem'\n  };\n  const roleButtonStyle = active => ({\n    flex: 1,\n    padding: '1rem',\n    border: active ? '2px solid #667eea' : '2px solid #ddd',\n    borderRadius: '10px',\n    background: active ? '#667eea' : 'white',\n    color: active ? 'white' : '#666',\n    cursor: 'pointer',\n    fontWeight: '600',\n    margin: '0 0.5rem'\n  });\n  const inputStyle = {\n    width: '100%',\n    padding: '1rem',\n    border: '2px solid #ddd',\n    borderRadius: '10px',\n    fontSize: '1rem',\n    marginBottom: '1rem'\n  };\n  const buttonStyle = {\n    width: '100%',\n    padding: '1rem',\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    color: 'white',\n    border: 'none',\n    borderRadius: '10px',\n    fontSize: '1.1rem',\n    fontWeight: '600',\n    cursor: isLoading ? 'not-allowed' : 'pointer',\n    opacity: isLoading ? 0.7 : 1\n  };\n  const isSuccessMessage = message.includes('successful') || message.includes('Welcome') || message.includes('logged out');\n  const isErrorMessage = message.includes('failed') || message.includes('error') || message.includes('Error') || message.includes('Invalid') || message.includes('incorrect') || message.includes('mismatch') || message.includes('required') || message.includes('Please') || message.includes('No account') || message.includes('disabled') || message.includes('Too many') || message.includes('Network');\n  const messageStyle = {\n    padding: '1rem',\n    borderRadius: '10px',\n    marginTop: '1rem',\n    background: isSuccessMessage ? '#d4edda' : isErrorMessage ? '#f8d7da' : '#e2e3e5',\n    color: isSuccessMessage ? '#155724' : isErrorMessage ? '#721c24' : '#383d41',\n    border: `1px solid ${isSuccessMessage ? '#c3e6cb' : isErrorMessage ? '#f5c6cb' : '#d6d8db'}`,\n    fontSize: '0.9rem',\n    textAlign: 'center',\n    fontWeight: '500'\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          body {\n            margin: 0;\n            padding: 0;\n            overflow-x: hidden;\n          }\n\n          /* Fallback background in case image doesn't load */\n          .login-container {\n            background-image:\n              linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)),\n              url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\"><defs><linearGradient id=\"sky\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\"><stop offset=\"0%\" style=\"stop-color:%2387CEEB;stop-opacity:1\" /><stop offset=\"100%\" style=\"stop-color:%23B0E0E6;stop-opacity:1\" /></linearGradient></defs><rect fill=\"url(%23sky)\" width=\"1200\" height=\"600\"/><rect fill=\"%2398FB98\" y=\"600\" width=\"1200\" height=\"200\"/><rect fill=\"%23F0E68C\" x=\"50\" y=\"200\" width=\"280\" height=\"400\" rx=\"10\"/><rect fill=\"%23E6E6FA\" x=\"350\" y=\"150\" width=\"350\" height=\"450\" rx=\"10\"/><rect fill=\"%23F0E68C\" x=\"720\" y=\"180\" width=\"250\" height=\"420\" rx=\"10\"/><rect fill=\"%23DDA0DD\" x=\"990\" y=\"220\" width=\"180\" height=\"380\" rx=\"10\"/><circle fill=\"%23228B22\" cx=\"150\" cy=\"650\" r=\"40\"/><circle fill=\"%23228B22\" cx=\"400\" cy=\"670\" r=\"35\"/><circle fill=\"%23228B22\" cx=\"700\" cy=\"660\" r=\"45\"/><circle fill=\"%23228B22\" cx=\"1000\" cy=\"650\" r=\"38\"/></svg>');\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: containerStyle,\n      className: \"login-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: headerStyle,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              margin: '0 auto 1rem',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo-light.png\",\n              alt: \"InnovAid Logo\",\n              style: {\n                height: '100px',\n                width: 'auto',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              fontSize: '1.1rem'\n            },\n            children: \"Innovaid for SECE - Campus Utilities Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: roleButtonStyle(selectedRole === 'user'),\n            onClick: () => {\n              setSelectedRole('user');\n              // Don't copy data between roles - keep forms completely separate\n            },\n            children: \"\\uD83D\\uDC68\\u200D\\uD83C\\uDF93 Student\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: roleButtonStyle(selectedRole === 'admin'),\n            onClick: () => {\n              setSelectedRole('admin');\n              // Don't copy data between roles - keep forms completely separate\n            },\n            children: \"\\uD83D\\uDEE1\\uFE0F Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [!isLogin && /*#__PURE__*/_jsxDEV(\"input\", {\n            style: inputStyle,\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name || '',\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            style: inputStyle,\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email Address\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            style: inputStyle,\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 11\n          }, this), !isLogin && otpState.isOTPSent && !otpState.isOTPVerified && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              style: inputStyle,\n              type: \"text\",\n              placeholder: \"Enter 6-digit OTP\",\n              value: otpState.otpCode,\n              onChange: handleOTPChange,\n              maxLength: 6,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginTop: '0.5rem',\n                fontSize: '0.9rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#666'\n                },\n                children: [\"OTP expires in: \", Math.max(0, Math.ceil((otpState.otpExpiry - Date.now()) / 60000)), \" min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: handleResendOTP,\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#667eea',\n                  cursor: 'pointer',\n                  textDecoration: 'underline',\n                  fontSize: '0.9rem'\n                },\n                children: \"Resend OTP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), !isLogin && selectedRole === 'user' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              style: inputStyle,\n              type: \"text\",\n              name: \"studentId\",\n              placeholder: \"Student ID\",\n              value: formData.studentId || '',\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              style: inputStyle,\n              name: \"department\",\n              value: formData.department || '',\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"AIDS\",\n                children: \"AIDS (Artificial Intelligence & Data Science)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CSE\",\n                children: \"CSE (Computer Science & Engineering)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"EEE\",\n                children: \"EEE (Electrical & Electronics Engineering)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ECE\",\n                children: \"ECE (Electronics & Communication Engineering)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"MECH\",\n                children: \"MECH (Mechanical Engineering)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CSBS\",\n                children: \"CSBS (Computer Science & Business Systems)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"AIML\",\n                children: \"AIML (Artificial Intelligence & Machine Learning)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CYBER\",\n                children: \"CYBER (Cyber Security)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CCE\",\n                children: \"CCE (Computer & Communication Engineering)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              style: inputStyle,\n              name: \"year\",\n              value: formData.year || '',\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"1\",\n                children: \"1st Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"2\",\n                children: \"2nd Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"3\",\n                children: \"3rd Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"4\",\n                children: \"4th Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              style: inputStyle,\n              type: \"tel\",\n              name: \"phone\",\n              placeholder: \"Phone Number (Optional)\",\n              value: formData.phone || '',\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true), !isLogin && selectedRole === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              style: inputStyle,\n              type: \"text\",\n              name: \"department\",\n              placeholder: \"Department/Office (e.g., Administration)\",\n              value: formData.department || '',\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              style: inputStyle,\n              type: \"tel\",\n              name: \"phone\",\n              placeholder: \"Phone Number\",\n              value: formData.phone || '',\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            style: buttonStyle,\n            disabled: isLoading,\n            children: isLoading ? '⏳ Processing...' : isLogin ? `🔐 Sign In as ${selectedRole === 'user' ? 'Student' : 'Admin'}` : !otpState.isOTPSent ? `📧 Send OTP` : !otpState.isOTPVerified ? `🔐 Verify OTP` : `📝 Complete Registration`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            background: 'none',\n            border: 'none',\n            color: '#666',\n            cursor: 'pointer',\n            fontSize: '0.9rem',\n            width: '100%',\n            padding: '1rem',\n            marginTop: '1rem'\n          },\n          onClick: () => {\n            setIsLogin(!isLogin);\n            setMessage('');\n            // Clear both form states when toggling\n            setStudentFormData({\n              email: '',\n              password: '',\n              role: 'user',\n              name: '',\n              studentId: '',\n              department: '',\n              year: undefined,\n              phone: ''\n            });\n            setAdminFormData({\n              email: '',\n              password: '',\n              role: 'admin',\n              name: '',\n              department: '',\n              phone: ''\n            });\n            // Reset OTP state\n            setOtpState({\n              isOTPSent: false,\n              isOTPVerified: false,\n              otpCode: '',\n              generatedOTP: '',\n              otpExpiry: 0\n            });\n          },\n          children: isLogin ? \"Don't have an account? Register here\" : \"Already have an account? Sign in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 9\n        }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: messageStyle,\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\nexport default SimpleLogin;", "map": {"version": 3, "names": ["React", "useState", "registerUser", "loginUser", "sendOTPEmail", "UserDashboard", "AdminDashboard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleLogin", "selectedR<PERSON>", "setSelectedRole", "is<PERSON>ogin", "setIsLogin", "studentFormData", "setStudentFormData", "email", "password", "role", "name", "studentId", "department", "year", "undefined", "phone", "adminFormData", "setAdminFormData", "formData", "isLoading", "setIsLoading", "message", "setMessage", "currentUser", "setCurrentUser", "otpState", "setOtpState", "isOTPSent", "isOTPVerified", "otpCode", "generatedOTP", "otpExpiry", "generateOTP", "Math", "floor", "random", "toString", "sendOTP", "otp", "expiry", "Date", "now", "expiryTime", "toLocaleString", "emailSent", "prev", "error", "console", "verifyOTP", "length", "handleInputChange", "e", "value", "target", "parseInt", "handleOTPChange", "replace", "slice", "handleResendOTP", "handleSubmit", "preventDefault", "result", "success", "user", "userRole", "selectedUserRole", "correctRoleText", "selectedRoleText", "localStorage", "setItem", "JSON", "stringify", "trim", "endsWith", "otpSent", "isOTPValid", "userData", "studentData", "adminData", "handleLogout", "logoutUser", "removeItem", "useEffect", "onLogout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "containerStyle", "minHeight", "height", "background", "backgroundSize", "backgroundPosition", "backgroundRepeat", "backgroundAttachment", "display", "alignItems", "justifyContent", "fontFamily", "padding", "overflow", "cardStyle", "<PERSON><PERSON>ilter", "borderRadius", "boxShadow", "border", "max<PERSON><PERSON><PERSON>", "width", "position", "zIndex", "headerStyle", "textAlign", "marginBottom", "titleStyle", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "roleButtonStyle", "active", "flex", "color", "cursor", "margin", "inputStyle", "buttonStyle", "opacity", "isSuccessMessage", "includes", "isErrorMessage", "messageStyle", "marginTop", "children", "style", "className", "src", "alt", "objectFit", "onClick", "onSubmit", "type", "placeholder", "onChange", "required", "max<PERSON><PERSON><PERSON>", "max", "ceil", "textDecoration", "disabled"], "sources": ["B:/ZERO DAY/ZeroDay-Innovaid/beautiful-react-ui/src/components/SimpleLogin.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\n// @ts-ignore\r\nimport { registerUser, loginUser, AppUser } from '../firebase/auth';\r\nimport { sendOTPEmail } from '../services/emailService';\r\nimport UserDashboard from './UserDashboard';\r\nimport AdminDashboard from './AdminDashboard';\r\n\r\ninterface BaseFormData {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  role: 'user' | 'admin';\r\n}\r\n\r\ninterface StudentFormData extends BaseFormData {\r\n  role: 'user';\r\n  studentId?: string;\r\n  department?: string;\r\n  year?: number;\r\n  phone?: string;\r\n}\r\n\r\ninterface AdminFormData extends BaseFormData {\r\n  role: 'admin';\r\n  department?: string;\r\n  phone?: string;\r\n}\r\n\r\n// Union type for form data (used in formData variable)\r\ntype LoginFormData = StudentFormData | AdminFormData;\r\n\r\n// OTP verification state\r\ninterface OTPState {\r\n  isOTPSent: boolean;\r\n  isOTPVerified: boolean;\r\n  otpCode: string;\r\n  generatedOTP: string;\r\n  otpExpiry: number;\r\n}\r\n\r\nconst SimpleLogin: React.FC = () => {\r\n  const [selectedRole, setSelectedRole] = useState<'user' | 'admin'>('user');\r\n  const [isLogin, setIsLogin] = useState(true);\r\n\r\n  // Separate form states for admin and student\r\n  const [studentFormData, setStudentFormData] = useState<StudentFormData>({\r\n    email: '',\r\n    password: '',\r\n    role: 'user',\r\n    name: '',\r\n    studentId: '',\r\n    department: '',\r\n    year: undefined,\r\n    phone: ''\r\n  });\r\n\r\n  const [adminFormData, setAdminFormData] = useState<AdminFormData>({\r\n    email: '',\r\n    password: '',\r\n    role: 'admin',\r\n    name: '',\r\n    department: '',\r\n    phone: ''\r\n  });\r\n\r\n  // Get current form data based on selected role\r\n  const formData = selectedRole === 'user' ? studentFormData : adminFormData;\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [currentUser, setCurrentUser] = useState<AppUser | null>(null);\r\n\r\n  // OTP verification state\r\n  const [otpState, setOtpState] = useState<OTPState>({\r\n    isOTPSent: false,\r\n    isOTPVerified: false,\r\n    otpCode: '',\r\n    generatedOTP: '',\r\n    otpExpiry: 0\r\n  });\r\n\r\n  // Generate 6-digit OTP\r\n  const generateOTP = (): string => {\r\n    return Math.floor(100000 + Math.random() * 900000).toString();\r\n  };\r\n\r\n  // Send OTP via email\r\n  const sendOTP = async (email: string): Promise<boolean> => {\r\n    try {\r\n      const otp = generateOTP();\r\n      const expiry = Date.now() + 10 * 60 * 1000; // 10 minutes expiry\r\n      const expiryTime = new Date(expiry).toLocaleString();\r\n\r\n      // Send OTP email\r\n      const emailSent = await sendOTPEmail({\r\n        email,\r\n        otp,\r\n        name: formData.name,\r\n        expiryTime\r\n      });\r\n\r\n      if (!emailSent) {\r\n        setMessage('❌ Failed to send OTP email. Please check your email address and try again.');\r\n        return false;\r\n      }\r\n\r\n      setOtpState(prev => ({\r\n        ...prev,\r\n        isOTPSent: true,\r\n        generatedOTP: otp,\r\n        otpExpiry: expiry\r\n      }));\r\n\r\n      setMessage(`📧 OTP sent to ${email}. Please check your email and enter the 6-digit code below. (Check spam folder if not found)`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error sending OTP:', error);\r\n      setMessage('❌ Failed to send OTP. Please try again.');\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Verify OTP\r\n  const verifyOTP = (): boolean => {\r\n    if (!otpState.otpCode || otpState.otpCode.length !== 6) {\r\n      setMessage('❌ Please enter a valid 6-digit OTP.');\r\n      return false;\r\n    }\r\n\r\n    if (Date.now() > otpState.otpExpiry) {\r\n      setMessage('❌ OTP has expired. Please request a new one.');\r\n      setOtpState(prev => ({ ...prev, isOTPSent: false, otpCode: '', generatedOTP: '' }));\r\n      return false;\r\n    }\r\n\r\n    if (otpState.otpCode !== otpState.generatedOTP) {\r\n      setMessage('❌ Invalid OTP. Please check and try again.');\r\n      return false;\r\n    }\r\n\r\n    setOtpState(prev => ({ ...prev, isOTPVerified: true }));\r\n    setMessage('✅ OTP verified successfully! Proceeding with registration...');\r\n    return true;\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n\r\n    if (selectedRole === 'user') {\r\n      setStudentFormData(prev => ({\r\n        ...prev,\r\n        [name]: name === 'year' ? parseInt(value) : value\r\n      }));\r\n    } else {\r\n      setAdminFormData(prev => ({\r\n        ...prev,\r\n        [name]: value\r\n      }));\r\n    }\r\n  };\r\n\r\n  // Handle OTP input change\r\n  const handleOTPChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value.replace(/\\D/g, '').slice(0, 6); // Only digits, max 6\r\n    setOtpState(prev => ({ ...prev, otpCode: value }));\r\n  };\r\n\r\n  // Resend OTP\r\n  const handleResendOTP = async () => {\r\n    setOtpState(prev => ({\r\n      ...prev,\r\n      isOTPSent: false,\r\n      otpCode: '',\r\n      generatedOTP: '',\r\n      otpExpiry: 0\r\n    }));\r\n    await sendOTP(formData.email);\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    setMessage('');\r\n\r\n    try {\r\n      if (isLogin) {\r\n        // Login with Firebase\r\n        const result = await loginUser(formData.email, formData.password);\r\n\r\n        if (result.success && result.user) {\r\n          // Validate that the selected role matches the user's actual role\r\n          const userRole = result.user.role;\r\n          const selectedUserRole = selectedRole === 'user' ? 'user' : 'admin';\r\n\r\n          if (userRole !== selectedUserRole) {\r\n            const correctRoleText = userRole === 'user' ? 'Student' : 'Admin';\r\n            const selectedRoleText = selectedUserRole === 'user' ? 'Student' : 'Admin';\r\n            setMessage(`Role mismatch: You are registered as a ${correctRoleText}, but trying to login as ${selectedRoleText}. Please select the correct role above.`);\r\n            return;\r\n          }\r\n\r\n          setMessage(`Welcome back, ${result.user.name}! Login successful.`);\r\n          localStorage.setItem('user', JSON.stringify(result.user));\r\n          setCurrentUser(result.user);\r\n        } else {\r\n          setMessage(result.message);\r\n        }\r\n      } else {\r\n        // Register with Firebase - Multi-step process with OTP\r\n        if (!formData.name || formData.name.trim() === '') {\r\n          setMessage('Please enter your full name to complete registration.');\r\n          return;\r\n        }\r\n\r\n        // Validate email domain for institutional email\r\n        if (selectedRole === 'user') {\r\n          // Student email validation\r\n          if (!formData.email.endsWith('@sece.ac.in')) {\r\n            setMessage('Please use your institutional email address ending with @sece.ac.in');\r\n            return;\r\n          }\r\n        } else {\r\n          // Admin email validation\r\n          if (!formData.email.endsWith('<EMAIL>')) {\r\n            setMessage('Admin registration requires email <NAME_EMAIL>');\r\n            return;\r\n          }\r\n        }\r\n\r\n        // Step 1: Send OTP if not sent yet\r\n        if (!otpState.isOTPSent) {\r\n          const otpSent = await sendOTP(formData.email);\r\n          if (!otpSent) {\r\n            return;\r\n          }\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Step 2: Verify OTP if sent but not verified\r\n        if (otpState.isOTPSent && !otpState.isOTPVerified) {\r\n          const isOTPValid = verifyOTP();\r\n          if (!isOTPValid) {\r\n            setIsLoading(false);\r\n            return;\r\n          }\r\n          // Continue to registration after OTP verification\r\n        }\r\n\r\n        // Handle different form types for registration\r\n        let userData;\r\n\r\n        if (selectedRole === 'user') {\r\n          const studentData = formData as StudentFormData;\r\n          userData = {\r\n            name: studentData.name,\r\n            role: 'user' as const,\r\n            studentId: studentData.studentId,\r\n            department: studentData.department,\r\n            year: studentData.year,\r\n            phone: studentData.phone\r\n          };\r\n        } else {\r\n          const adminData = formData as AdminFormData;\r\n          userData = {\r\n            name: adminData.name,\r\n            role: 'admin' as const,\r\n            department: adminData.department,\r\n            phone: adminData.phone\r\n          };\r\n        }\r\n\r\n        const result = await registerUser(formData.email, formData.password, userData);\r\n\r\n        if (result.success && result.user) {\r\n          setMessage(`Registration successful! Welcome to the platform, ${result.user.name}!`);\r\n          localStorage.setItem('user', JSON.stringify(result.user));\r\n          setCurrentUser(result.user);\r\n        } else {\r\n          setMessage(result.message);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Authentication error:', error);\r\n      setMessage('Something went wrong. Please check your connection and try again.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      // @ts-ignore\r\n      const { logoutUser } = await import('../firebase/auth');\r\n      const result = await logoutUser();\r\n\r\n      if (result.success) {\r\n        localStorage.removeItem('user');\r\n        setCurrentUser(null);\r\n        setMessage('You have been logged out successfully.');\r\n        // Reset both form states\r\n        setStudentFormData({\r\n          email: '',\r\n          password: '',\r\n          role: 'user',\r\n          name: '',\r\n          studentId: '',\r\n          department: '',\r\n          year: undefined,\r\n          phone: ''\r\n        });\r\n        setAdminFormData({\r\n          email: '',\r\n          password: '',\r\n          role: 'admin',\r\n          name: '',\r\n          department: '',\r\n          phone: ''\r\n        });\r\n      } else {\r\n        setMessage(`Logout failed: ${result.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      // Fallback logout\r\n      localStorage.removeItem('user');\r\n      setCurrentUser(null);\r\n      setMessage('You have been logged out successfully.');\r\n      // Reset both form states\r\n      setStudentFormData({\r\n        email: '',\r\n        password: '',\r\n        role: 'user',\r\n        name: '',\r\n        studentId: '',\r\n        department: '',\r\n        year: undefined,\r\n        phone: ''\r\n      });\r\n      setAdminFormData({\r\n        email: '',\r\n        password: '',\r\n        role: 'admin',\r\n        name: '',\r\n        department: '',\r\n        phone: ''\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // TEMPORARILY DISABLED AUTO-LOGIN FOR TESTING LOGIN VALIDATION\r\n  // Check if user is logged in on component mount and set up Firebase auth listener\r\n  React.useEffect(() => {\r\n    // Clear any existing session to test login properly\r\n    localStorage.removeItem('user');\r\n    setCurrentUser(null);\r\n\r\n    // Commented out auto-login for testing\r\n    /*\r\n    const setupAuthListener = async () => {\r\n      try {\r\n        // @ts-ignore\r\n        const { auth } = await import('../firebase/config');\r\n        // @ts-ignore\r\n        const { onAuthStateChanged } = await import('firebase/auth');\r\n        // @ts-ignore\r\n        const { getCurrentUserData } = await import('../firebase/auth');\r\n\r\n        const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\r\n          try {\r\n            if (firebaseUser) {\r\n              // User is signed in\r\n              console.log('🔐 Firebase user authenticated:', firebaseUser.uid);\r\n              const userData = await getCurrentUserData(firebaseUser);\r\n              if (userData) {\r\n                console.log('✅ User data loaded:', userData.name);\r\n                setCurrentUser(userData);\r\n                localStorage.setItem('user', JSON.stringify(userData));\r\n              } else {\r\n                console.warn('⚠️ No user data found for authenticated user');\r\n              }\r\n            } else {\r\n              // User is signed out\r\n              console.log('🔓 User signed out');\r\n              setCurrentUser(null);\r\n              localStorage.removeItem('user');\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Error in auth state change handler:', error);\r\n            // Don't set user state if there's an error\r\n            setCurrentUser(null);\r\n            localStorage.removeItem('user');\r\n          }\r\n        }, (error) => {\r\n          console.error('❌ Firebase auth state change error:', error);\r\n          setCurrentUser(null);\r\n          localStorage.removeItem('user');\r\n        });\r\n\r\n        return unsubscribe;\r\n      } catch (error) {\r\n        console.error('Error setting up auth listener:', error);\r\n        // Fallback to localStorage check\r\n        const userData = localStorage.getItem('user');\r\n        if (userData) {\r\n          try {\r\n            const user = JSON.parse(userData);\r\n            setCurrentUser(user);\r\n          } catch (error) {\r\n            console.error('Error parsing user data:', error);\r\n            localStorage.removeItem('user');\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    setupAuthListener();\r\n    */\r\n  }, []);\r\n\r\n  // If user is logged in, show appropriate dashboard\r\n  if (currentUser) {\r\n    if (currentUser.role === 'admin') {\r\n      return <AdminDashboard user={currentUser} onLogout={handleLogout} />;\r\n    } else {\r\n      return <UserDashboard user={currentUser} onLogout={handleLogout} />;\r\n    }\r\n  }\r\n\r\n  const containerStyle: React.CSSProperties = {\r\n    minHeight: '100vh',\r\n    height: '100vh',\r\n    background: `\r\n      linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)),\r\n      url('/college-building.jpg')\r\n    `,\r\n    backgroundSize: 'cover',\r\n    backgroundPosition: 'center',\r\n    backgroundRepeat: 'no-repeat',\r\n    backgroundAttachment: 'fixed',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    fontFamily: 'Arial, sans-serif',\r\n    padding: '2rem',\r\n    overflow: 'hidden'\r\n  };\r\n\r\n  const cardStyle: React.CSSProperties = {\r\n    background: 'rgba(255, 255, 255, 0.85)',\r\n    backdropFilter: 'blur(15px)',\r\n    borderRadius: '25px',\r\n    padding: '2.5rem',\r\n    boxShadow: '0 25px 50px rgba(0,0,0,0.15)',\r\n    border: '1px solid rgba(255, 255, 255, 0.2)',\r\n    maxWidth: '500px',\r\n    width: '100%',\r\n    position: 'relative',\r\n    zIndex: 1\r\n  };\r\n\r\n  const headerStyle: React.CSSProperties = {\r\n    textAlign: 'center',\r\n    marginBottom: '2rem'\r\n  };\r\n\r\n  const titleStyle: React.CSSProperties = {\r\n    fontSize: '2.5rem',\r\n    fontWeight: '800',\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    WebkitBackgroundClip: 'text',\r\n    WebkitTextFillColor: 'transparent',\r\n    marginBottom: '0.5rem'\r\n  };\r\n\r\n  const roleButtonStyle = (active: boolean): React.CSSProperties => ({\r\n    flex: 1,\r\n    padding: '1rem',\r\n    border: active ? '2px solid #667eea' : '2px solid #ddd',\r\n    borderRadius: '10px',\r\n    background: active ? '#667eea' : 'white',\r\n    color: active ? 'white' : '#666',\r\n    cursor: 'pointer',\r\n    fontWeight: '600',\r\n    margin: '0 0.5rem'\r\n  });\r\n\r\n  const inputStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    padding: '1rem',\r\n    border: '2px solid #ddd',\r\n    borderRadius: '10px',\r\n    fontSize: '1rem',\r\n    marginBottom: '1rem'\r\n  };\r\n\r\n  const buttonStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    padding: '1rem',\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    color: 'white',\r\n    border: 'none',\r\n    borderRadius: '10px',\r\n    fontSize: '1.1rem',\r\n    fontWeight: '600',\r\n    cursor: isLoading ? 'not-allowed' : 'pointer',\r\n    opacity: isLoading ? 0.7 : 1\r\n  };\r\n\r\n  const isSuccessMessage = message.includes('successful') || message.includes('Welcome') || message.includes('logged out');\r\n  const isErrorMessage = message.includes('failed') || message.includes('error') || message.includes('Error') ||\r\n                         message.includes('Invalid') || message.includes('incorrect') || message.includes('mismatch') ||\r\n                         message.includes('required') || message.includes('Please') || message.includes('No account') ||\r\n                         message.includes('disabled') || message.includes('Too many') || message.includes('Network');\r\n\r\n  const messageStyle: React.CSSProperties = {\r\n    padding: '1rem',\r\n    borderRadius: '10px',\r\n    marginTop: '1rem',\r\n    background: isSuccessMessage ? '#d4edda' : isErrorMessage ? '#f8d7da' : '#e2e3e5',\r\n    color: isSuccessMessage ? '#155724' : isErrorMessage ? '#721c24' : '#383d41',\r\n    border: `1px solid ${isSuccessMessage ? '#c3e6cb' : isErrorMessage ? '#f5c6cb' : '#d6d8db'}`,\r\n    fontSize: '0.9rem',\r\n    textAlign: 'center',\r\n    fontWeight: '500'\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <style>\r\n        {`\r\n          body {\r\n            margin: 0;\r\n            padding: 0;\r\n            overflow-x: hidden;\r\n          }\r\n\r\n          /* Fallback background in case image doesn't load */\r\n          .login-container {\r\n            background-image:\r\n              linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)),\r\n              url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\"><defs><linearGradient id=\"sky\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\"><stop offset=\"0%\" style=\"stop-color:%2387CEEB;stop-opacity:1\" /><stop offset=\"100%\" style=\"stop-color:%23B0E0E6;stop-opacity:1\" /></linearGradient></defs><rect fill=\"url(%23sky)\" width=\"1200\" height=\"600\"/><rect fill=\"%2398FB98\" y=\"600\" width=\"1200\" height=\"200\"/><rect fill=\"%23F0E68C\" x=\"50\" y=\"200\" width=\"280\" height=\"400\" rx=\"10\"/><rect fill=\"%23E6E6FA\" x=\"350\" y=\"150\" width=\"350\" height=\"450\" rx=\"10\"/><rect fill=\"%23F0E68C\" x=\"720\" y=\"180\" width=\"250\" height=\"420\" rx=\"10\"/><rect fill=\"%23DDA0DD\" x=\"990\" y=\"220\" width=\"180\" height=\"380\" rx=\"10\"/><circle fill=\"%23228B22\" cx=\"150\" cy=\"650\" r=\"40\"/><circle fill=\"%23228B22\" cx=\"400\" cy=\"670\" r=\"35\"/><circle fill=\"%23228B22\" cx=\"700\" cy=\"660\" r=\"45\"/><circle fill=\"%23228B22\" cx=\"1000\" cy=\"650\" r=\"38\"/></svg>');\r\n          }\r\n        `}\r\n      </style>\r\n      <div style={containerStyle} className=\"login-container\">\r\n        <div style={cardStyle}>\r\n        <div style={headerStyle}>\r\n          <div style={{\r\n            margin: '0 auto 1rem',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center'\r\n          }}>\r\n            <img\r\n              src=\"/logo-light.png\"\r\n              alt=\"InnovAid Logo\"\r\n              style={{\r\n                height: '100px',\r\n                width: 'auto',\r\n                objectFit: 'contain'\r\n              }}\r\n            />\r\n          </div>\r\n          <p style={{ color: '#666', fontSize: '1.1rem' }}>Innovaid for SECE - Campus Utilities Platform</p>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', marginBottom: '2rem' }}>\r\n          <button\r\n            style={roleButtonStyle(selectedRole === 'user')}\r\n            onClick={() => {\r\n              setSelectedRole('user');\r\n              // Don't copy data between roles - keep forms completely separate\r\n            }}\r\n          >\r\n            👨‍🎓 Student\r\n          </button>\r\n          <button\r\n            style={roleButtonStyle(selectedRole === 'admin')}\r\n            onClick={() => {\r\n              setSelectedRole('admin');\r\n              // Don't copy data between roles - keep forms completely separate\r\n            }}\r\n          >\r\n            🛡️ Admin\r\n          </button>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          {!isLogin && (\r\n            <input\r\n              style={inputStyle}\r\n              type=\"text\"\r\n              name=\"name\"\r\n              placeholder=\"Full Name\"\r\n              value={formData.name || ''}\r\n              onChange={handleInputChange}\r\n              required\r\n            />\r\n          )}\r\n\r\n          <input\r\n            style={inputStyle}\r\n            type=\"email\"\r\n            name=\"email\"\r\n            placeholder=\"Email Address\"\r\n            value={formData.email}\r\n            onChange={handleInputChange}\r\n            required\r\n          />\r\n\r\n          <input\r\n            style={inputStyle}\r\n            type=\"password\"\r\n            name=\"password\"\r\n            placeholder=\"Password\"\r\n            value={formData.password}\r\n            onChange={handleInputChange}\r\n            required\r\n          />\r\n\r\n          {/* OTP Input Field for Registration */}\r\n          {!isLogin && otpState.isOTPSent && !otpState.isOTPVerified && (\r\n            <div style={{ marginBottom: '1rem' }}>\r\n              <input\r\n                style={inputStyle}\r\n                type=\"text\"\r\n                placeholder=\"Enter 6-digit OTP\"\r\n                value={otpState.otpCode}\r\n                onChange={handleOTPChange}\r\n                maxLength={6}\r\n                required\r\n              />\r\n              <div style={{\r\n                display: 'flex',\r\n                justifyContent: 'space-between',\r\n                alignItems: 'center',\r\n                marginTop: '0.5rem',\r\n                fontSize: '0.9rem'\r\n              }}>\r\n                <span style={{ color: '#666' }}>\r\n                  OTP expires in: {Math.max(0, Math.ceil((otpState.otpExpiry - Date.now()) / 60000))} min\r\n                </span>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={handleResendOTP}\r\n                  style={{\r\n                    background: 'none',\r\n                    border: 'none',\r\n                    color: '#667eea',\r\n                    cursor: 'pointer',\r\n                    textDecoration: 'underline',\r\n                    fontSize: '0.9rem'\r\n                  }}\r\n                >\r\n                  Resend OTP\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!isLogin && selectedRole === 'user' && (\r\n            <>\r\n              <input\r\n                style={inputStyle}\r\n                type=\"text\"\r\n                name=\"studentId\"\r\n                placeholder=\"Student ID\"\r\n                value={(formData as StudentFormData).studentId || ''}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <select\r\n                style={inputStyle}\r\n                name=\"department\"\r\n                value={formData.department || ''}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\">Select Department</option>\r\n                <option value=\"AIDS\">AIDS (Artificial Intelligence & Data Science)</option>\r\n                <option value=\"CSE\">CSE (Computer Science & Engineering)</option>\r\n                <option value=\"EEE\">EEE (Electrical & Electronics Engineering)</option>\r\n                <option value=\"ECE\">ECE (Electronics & Communication Engineering)</option>\r\n                <option value=\"MECH\">MECH (Mechanical Engineering)</option>\r\n                <option value=\"CSBS\">CSBS (Computer Science & Business Systems)</option>\r\n                <option value=\"AIML\">AIML (Artificial Intelligence & Machine Learning)</option>\r\n                <option value=\"CYBER\">CYBER (Cyber Security)</option>\r\n                <option value=\"CCE\">CCE (Computer & Communication Engineering)</option>\r\n              </select>\r\n              <select\r\n                style={inputStyle}\r\n                name=\"year\"\r\n                value={(formData as StudentFormData).year || ''}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\">Select Year</option>\r\n                <option value=\"1\">1st Year</option>\r\n                <option value=\"2\">2nd Year</option>\r\n                <option value=\"3\">3rd Year</option>\r\n                <option value=\"4\">4th Year</option>\r\n              </select>\r\n              <input\r\n                style={inputStyle}\r\n                type=\"tel\"\r\n                name=\"phone\"\r\n                placeholder=\"Phone Number (Optional)\"\r\n                value={formData.phone || ''}\r\n                onChange={handleInputChange}\r\n              />\r\n            </>\r\n          )}\r\n\r\n          {!isLogin && selectedRole === 'admin' && (\r\n            <>\r\n              <input\r\n                style={inputStyle}\r\n                type=\"text\"\r\n                name=\"department\"\r\n                placeholder=\"Department/Office (e.g., Administration)\"\r\n                value={formData.department || ''}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                style={inputStyle}\r\n                type=\"tel\"\r\n                name=\"phone\"\r\n                placeholder=\"Phone Number\"\r\n                value={formData.phone || ''}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n            </>\r\n          )}\r\n\r\n          <button type=\"submit\" style={buttonStyle} disabled={isLoading}>\r\n            {isLoading ? '⏳ Processing...' :\r\n             isLogin ? `🔐 Sign In as ${selectedRole === 'user' ? 'Student' : 'Admin'}` :\r\n             !otpState.isOTPSent ? `📧 Send OTP` :\r\n             !otpState.isOTPVerified ? `🔐 Verify OTP` :\r\n             `📝 Complete Registration`}\r\n          </button>\r\n        </form>\r\n\r\n        <button\r\n          style={{\r\n            background: 'none',\r\n            border: 'none',\r\n            color: '#666',\r\n            cursor: 'pointer',\r\n            fontSize: '0.9rem',\r\n            width: '100%',\r\n            padding: '1rem',\r\n            marginTop: '1rem'\r\n          }}\r\n          onClick={() => {\r\n            setIsLogin(!isLogin);\r\n            setMessage('');\r\n            // Clear both form states when toggling\r\n            setStudentFormData({\r\n              email: '',\r\n              password: '',\r\n              role: 'user',\r\n              name: '',\r\n              studentId: '',\r\n              department: '',\r\n              year: undefined,\r\n              phone: ''\r\n            });\r\n            setAdminFormData({\r\n              email: '',\r\n              password: '',\r\n              role: 'admin',\r\n              name: '',\r\n              department: '',\r\n              phone: ''\r\n            });\r\n            // Reset OTP state\r\n            setOtpState({\r\n              isOTPSent: false,\r\n              isOTPVerified: false,\r\n              otpCode: '',\r\n              generatedOTP: '',\r\n              otpExpiry: 0\r\n            });\r\n          }}\r\n        >\r\n          {isLogin ? \"Don't have an account? Register here\" : \"Already have an account? Sign in\"}\r\n        </button>\r\n\r\n\r\n\r\n        {message && <div style={messageStyle}>{message}</div>}\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default SimpleLogin;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC;AACA,SAASC,YAAY,EAAEC,SAAS,QAAiB,kBAAkB;AACnE,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;;AAuB7C;;AAGA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAmB,MAAM,CAAC;EAC1E,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAkB;IACtEiB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAEC,SAAS;IACfC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAgB;IAChEiB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,EAAE;IACRE,UAAU,EAAE,EAAE;IACdG,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMG,QAAQ,GAAGjB,YAAY,KAAK,MAAM,GAAGI,eAAe,GAAGW,aAAa;EAC1E,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAiB,IAAI,CAAC;;EAEpE;EACA,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAW;IACjDqC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAc;IAChC,OAAOC,IAAI,CAACC,KAAK,CAAC,MAAM,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,QAAQ,CAAC,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,MAAO9B,KAAa,IAAuB;IACzD,IAAI;MACF,MAAM+B,GAAG,GAAGN,WAAW,CAAC,CAAC;MACzB,MAAMO,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;MAC5C,MAAMC,UAAU,GAAG,IAAIF,IAAI,CAACD,MAAM,CAAC,CAACI,cAAc,CAAC,CAAC;;MAEpD;MACA,MAAMC,SAAS,GAAG,MAAMnD,YAAY,CAAC;QACnCc,KAAK;QACL+B,GAAG;QACH5B,IAAI,EAAEQ,QAAQ,CAACR,IAAI;QACnBgC;MACF,CAAC,CAAC;MAEF,IAAI,CAACE,SAAS,EAAE;QACdtB,UAAU,CAAC,4EAA4E,CAAC;QACxF,OAAO,KAAK;MACd;MAEAI,WAAW,CAACmB,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPlB,SAAS,EAAE,IAAI;QACfG,YAAY,EAAEQ,GAAG;QACjBP,SAAS,EAAEQ;MACb,CAAC,CAAC,CAAC;MAEHjB,UAAU,CAAC,kBAAkBf,KAAK,8FAA8F,CAAC;MACjI,OAAO,IAAI;IACb,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CxB,UAAU,CAAC,yCAAyC,CAAC;MACrD,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM0B,SAAS,GAAGA,CAAA,KAAe;IAC/B,IAAI,CAACvB,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACI,OAAO,CAACoB,MAAM,KAAK,CAAC,EAAE;MACtD3B,UAAU,CAAC,qCAAqC,CAAC;MACjD,OAAO,KAAK;IACd;IAEA,IAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhB,QAAQ,CAACM,SAAS,EAAE;MACnCT,UAAU,CAAC,8CAA8C,CAAC;MAC1DI,WAAW,CAACmB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElB,SAAS,EAAE,KAAK;QAAEE,OAAO,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAC,CAAC,CAAC;MACnF,OAAO,KAAK;IACd;IAEA,IAAIL,QAAQ,CAACI,OAAO,KAAKJ,QAAQ,CAACK,YAAY,EAAE;MAC9CR,UAAU,CAAC,4CAA4C,CAAC;MACxD,OAAO,KAAK;IACd;IAEAI,WAAW,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjB,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACvDN,UAAU,CAAC,8DAA8D,CAAC;IAC1E,OAAO,IAAI;EACb,CAAC;EAED,MAAM4B,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEzC,IAAI;MAAE0C;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAEhC,IAAIpD,YAAY,KAAK,MAAM,EAAE;MAC3BK,kBAAkB,CAACuC,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACP,CAACnC,IAAI,GAAGA,IAAI,KAAK,MAAM,GAAG4C,QAAQ,CAACF,KAAK,CAAC,GAAGA;MAC9C,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLnC,gBAAgB,CAAC4B,IAAI,KAAK;QACxB,GAAGA,IAAI;QACP,CAACnC,IAAI,GAAG0C;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAIJ,CAAsC,IAAK;IAClE,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7D/B,WAAW,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhB,OAAO,EAAEuB;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClChC,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPlB,SAAS,EAAE,KAAK;MAChBE,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;IACH,MAAMM,OAAO,CAACnB,QAAQ,CAACX,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMoD,YAAY,GAAG,MAAOR,CAAkB,IAAK;IACjDA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBxC,YAAY,CAAC,IAAI,CAAC;IAClBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAInB,OAAO,EAAE;QACX;QACA,MAAM0D,MAAM,GAAG,MAAMrE,SAAS,CAAC0B,QAAQ,CAACX,KAAK,EAAEW,QAAQ,CAACV,QAAQ,CAAC;QAEjE,IAAIqD,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,IAAI,EAAE;UACjC;UACA,MAAMC,QAAQ,GAAGH,MAAM,CAACE,IAAI,CAACtD,IAAI;UACjC,MAAMwD,gBAAgB,GAAGhE,YAAY,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO;UAEnE,IAAI+D,QAAQ,KAAKC,gBAAgB,EAAE;YACjC,MAAMC,eAAe,GAAGF,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;YACjE,MAAMG,gBAAgB,GAAGF,gBAAgB,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;YAC1E3C,UAAU,CAAC,0CAA0C4C,eAAe,4BAA4BC,gBAAgB,yCAAyC,CAAC;YAC1J;UACF;UAEA7C,UAAU,CAAC,iBAAiBuC,MAAM,CAACE,IAAI,CAACrD,IAAI,qBAAqB,CAAC;UAClE0D,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACV,MAAM,CAACE,IAAI,CAAC,CAAC;UACzDvC,cAAc,CAACqC,MAAM,CAACE,IAAI,CAAC;QAC7B,CAAC,MAAM;UACLzC,UAAU,CAACuC,MAAM,CAACxC,OAAO,CAAC;QAC5B;MACF,CAAC,MAAM;QACL;QACA,IAAI,CAACH,QAAQ,CAACR,IAAI,IAAIQ,QAAQ,CAACR,IAAI,CAAC8D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACjDlD,UAAU,CAAC,uDAAuD,CAAC;UACnE;QACF;;QAEA;QACA,IAAIrB,YAAY,KAAK,MAAM,EAAE;UAC3B;UACA,IAAI,CAACiB,QAAQ,CAACX,KAAK,CAACkE,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC3CnD,UAAU,CAAC,qEAAqE,CAAC;YACjF;UACF;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAACJ,QAAQ,CAACX,KAAK,CAACkE,QAAQ,CAAC,eAAe,CAAC,EAAE;YAC7CnD,UAAU,CAAC,6DAA6D,CAAC;YACzE;UACF;QACF;;QAEA;QACA,IAAI,CAACG,QAAQ,CAACE,SAAS,EAAE;UACvB,MAAM+C,OAAO,GAAG,MAAMrC,OAAO,CAACnB,QAAQ,CAACX,KAAK,CAAC;UAC7C,IAAI,CAACmE,OAAO,EAAE;YACZ;UACF;UACAtD,YAAY,CAAC,KAAK,CAAC;UACnB;QACF;;QAEA;QACA,IAAIK,QAAQ,CAACE,SAAS,IAAI,CAACF,QAAQ,CAACG,aAAa,EAAE;UACjD,MAAM+C,UAAU,GAAG3B,SAAS,CAAC,CAAC;UAC9B,IAAI,CAAC2B,UAAU,EAAE;YACfvD,YAAY,CAAC,KAAK,CAAC;YACnB;UACF;UACA;QACF;;QAEA;QACA,IAAIwD,QAAQ;QAEZ,IAAI3E,YAAY,KAAK,MAAM,EAAE;UAC3B,MAAM4E,WAAW,GAAG3D,QAA2B;UAC/C0D,QAAQ,GAAG;YACTlE,IAAI,EAAEmE,WAAW,CAACnE,IAAI;YACtBD,IAAI,EAAE,MAAe;YACrBE,SAAS,EAAEkE,WAAW,CAAClE,SAAS;YAChCC,UAAU,EAAEiE,WAAW,CAACjE,UAAU;YAClCC,IAAI,EAAEgE,WAAW,CAAChE,IAAI;YACtBE,KAAK,EAAE8D,WAAW,CAAC9D;UACrB,CAAC;QACH,CAAC,MAAM;UACL,MAAM+D,SAAS,GAAG5D,QAAyB;UAC3C0D,QAAQ,GAAG;YACTlE,IAAI,EAAEoE,SAAS,CAACpE,IAAI;YACpBD,IAAI,EAAE,OAAgB;YACtBG,UAAU,EAAEkE,SAAS,CAAClE,UAAU;YAChCG,KAAK,EAAE+D,SAAS,CAAC/D;UACnB,CAAC;QACH;QAEA,MAAM8C,MAAM,GAAG,MAAMtE,YAAY,CAAC2B,QAAQ,CAACX,KAAK,EAAEW,QAAQ,CAACV,QAAQ,EAAEoE,QAAQ,CAAC;QAE9E,IAAIf,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,IAAI,EAAE;UACjCzC,UAAU,CAAC,qDAAqDuC,MAAM,CAACE,IAAI,CAACrD,IAAI,GAAG,CAAC;UACpF0D,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACV,MAAM,CAACE,IAAI,CAAC,CAAC;UACzDvC,cAAc,CAACqC,MAAM,CAACE,IAAI,CAAC;QAC7B,CAAC,MAAM;UACLzC,UAAU,CAACuC,MAAM,CAACxC,OAAO,CAAC;QAC5B;MACF;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxB,UAAU,CAAC,mEAAmE,CAAC;IACjF,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAM;QAAEC;MAAW,CAAC,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC;MACvD,MAAMnB,MAAM,GAAG,MAAMmB,UAAU,CAAC,CAAC;MAEjC,IAAInB,MAAM,CAACC,OAAO,EAAE;QAClBM,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;QAC/BzD,cAAc,CAAC,IAAI,CAAC;QACpBF,UAAU,CAAC,wCAAwC,CAAC;QACpD;QACAhB,kBAAkB,CAAC;UACjBC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE,EAAE;UACRC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdC,IAAI,EAAEC,SAAS;UACfC,KAAK,EAAE;QACT,CAAC,CAAC;QACFE,gBAAgB,CAAC;UACfV,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,EAAE;UACRE,UAAU,EAAE,EAAE;UACdG,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLO,UAAU,CAAC,kBAAkBuC,MAAM,CAACxC,OAAO,EAAE,CAAC;MAChD;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACAsB,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/BzD,cAAc,CAAC,IAAI,CAAC;MACpBF,UAAU,CAAC,wCAAwC,CAAC;MACpD;MACAhB,kBAAkB,CAAC;QACjBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,EAAE;QACRC,SAAS,EAAE,EAAE;QACbC,UAAU,EAAE,EAAE;QACdC,IAAI,EAAEC,SAAS;QACfC,KAAK,EAAE;MACT,CAAC,CAAC;MACFE,gBAAgB,CAAC;QACfV,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,EAAE;QACRE,UAAU,EAAE,EAAE;QACdG,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAID;EACA;EACA1B,KAAK,CAAC6F,SAAS,CAAC,MAAM;IACpB;IACAd,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;IAC/BzD,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAIE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAID,WAAW,EAAE;IACf,IAAIA,WAAW,CAACd,IAAI,KAAK,OAAO,EAAE;MAChC,oBAAOZ,OAAA,CAACF,cAAc;QAACoE,IAAI,EAAExC,WAAY;QAAC4D,QAAQ,EAAEJ;MAAa;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACtE,CAAC,MAAM;MACL,oBAAO1F,OAAA,CAACH,aAAa;QAACqE,IAAI,EAAExC,WAAY;QAAC4D,QAAQ,EAAEJ;MAAa;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACrE;EACF;EAEA,MAAMC,cAAmC,GAAG;IAC1CC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE;AAChB;AACA;AACA,KAAK;IACDC,cAAc,EAAE,OAAO;IACvBC,kBAAkB,EAAE,QAAQ;IAC5BC,gBAAgB,EAAE,WAAW;IAC7BC,oBAAoB,EAAE,OAAO;IAC7BC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,mBAAmB;IAC/BC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,SAA8B,GAAG;IACrCX,UAAU,EAAE,2BAA2B;IACvCY,cAAc,EAAE,YAAY;IAC5BC,YAAY,EAAE,MAAM;IACpBJ,OAAO,EAAE,QAAQ;IACjBK,SAAS,EAAE,8BAA8B;IACzCC,MAAM,EAAE,oCAAoC;IAC5CC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,WAAgC,GAAG;IACvCC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE;EAChB,CAAC;EAED,MAAMC,UAA+B,GAAG;IACtCC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBzB,UAAU,EAAE,mDAAmD;IAC/D0B,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCL,YAAY,EAAE;EAChB,CAAC;EAED,MAAMM,eAAe,GAAIC,MAAe,KAA2B;IACjEC,IAAI,EAAE,CAAC;IACPrB,OAAO,EAAE,MAAM;IACfM,MAAM,EAAEc,MAAM,GAAG,mBAAmB,GAAG,gBAAgB;IACvDhB,YAAY,EAAE,MAAM;IACpBb,UAAU,EAAE6B,MAAM,GAAG,SAAS,GAAG,OAAO;IACxCE,KAAK,EAAEF,MAAM,GAAG,OAAO,GAAG,MAAM;IAChCG,MAAM,EAAE,SAAS;IACjBP,UAAU,EAAE,KAAK;IACjBQ,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,UAA+B,GAAG;IACtCjB,KAAK,EAAE,MAAM;IACbR,OAAO,EAAE,MAAM;IACfM,MAAM,EAAE,gBAAgB;IACxBF,YAAY,EAAE,MAAM;IACpBW,QAAQ,EAAE,MAAM;IAChBF,YAAY,EAAE;EAChB,CAAC;EAED,MAAMa,WAAgC,GAAG;IACvClB,KAAK,EAAE,MAAM;IACbR,OAAO,EAAE,MAAM;IACfT,UAAU,EAAE,mDAAmD;IAC/D+B,KAAK,EAAE,OAAO;IACdhB,MAAM,EAAE,MAAM;IACdF,YAAY,EAAE,MAAM;IACpBW,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBO,MAAM,EAAExG,SAAS,GAAG,aAAa,GAAG,SAAS;IAC7C4G,OAAO,EAAE5G,SAAS,GAAG,GAAG,GAAG;EAC7B,CAAC;EAED,MAAM6G,gBAAgB,GAAG3G,OAAO,CAAC4G,QAAQ,CAAC,YAAY,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,SAAS,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,YAAY,CAAC;EACxH,MAAMC,cAAc,GAAG7G,OAAO,CAAC4G,QAAQ,CAAC,QAAQ,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,OAAO,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,OAAO,CAAC,IACpF5G,OAAO,CAAC4G,QAAQ,CAAC,SAAS,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,WAAW,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,UAAU,CAAC,IAC5F5G,OAAO,CAAC4G,QAAQ,CAAC,UAAU,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,QAAQ,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,YAAY,CAAC,IAC5F5G,OAAO,CAAC4G,QAAQ,CAAC,UAAU,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,UAAU,CAAC,IAAI5G,OAAO,CAAC4G,QAAQ,CAAC,SAAS,CAAC;EAElH,MAAME,YAAiC,GAAG;IACxC/B,OAAO,EAAE,MAAM;IACfI,YAAY,EAAE,MAAM;IACpB4B,SAAS,EAAE,MAAM;IACjBzC,UAAU,EAAEqC,gBAAgB,GAAG,SAAS,GAAGE,cAAc,GAAG,SAAS,GAAG,SAAS;IACjFR,KAAK,EAAEM,gBAAgB,GAAG,SAAS,GAAGE,cAAc,GAAG,SAAS,GAAG,SAAS;IAC5ExB,MAAM,EAAE,aAAasB,gBAAgB,GAAG,SAAS,GAAGE,cAAc,GAAG,SAAS,GAAG,SAAS,EAAE;IAC5Ff,QAAQ,EAAE,QAAQ;IAClBH,SAAS,EAAE,QAAQ;IACnBI,UAAU,EAAE;EACd,CAAC;EAED,oBACEvH,OAAA,CAAAE,SAAA;IAAAsI,QAAA,gBACExI,OAAA;MAAAwI,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACR1F,OAAA;MAAKyI,KAAK,EAAE9C,cAAe;MAAC+C,SAAS,EAAC,iBAAiB;MAAAF,QAAA,eACrDxI,OAAA;QAAKyI,KAAK,EAAEhC,SAAU;QAAA+B,QAAA,gBACtBxI,OAAA;UAAKyI,KAAK,EAAEvB,WAAY;UAAAsB,QAAA,gBACtBxI,OAAA;YAAKyI,KAAK,EAAE;cACVV,MAAM,EAAE,aAAa;cACrB5B,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAmC,QAAA,eACAxI,OAAA;cACE2I,GAAG,EAAC,iBAAiB;cACrBC,GAAG,EAAC,eAAe;cACnBH,KAAK,EAAE;gBACL5C,MAAM,EAAE,OAAO;gBACfkB,KAAK,EAAE,MAAM;gBACb8B,SAAS,EAAE;cACb;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1F,OAAA;YAAGyI,KAAK,EAAE;cAAEZ,KAAK,EAAE,MAAM;cAAEP,QAAQ,EAAE;YAAS,CAAE;YAAAkB,QAAA,EAAC;UAA6C;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eAEN1F,OAAA;UAAKyI,KAAK,EAAE;YAAEtC,OAAO,EAAE,MAAM;YAAEiB,YAAY,EAAE;UAAO,CAAE;UAAAoB,QAAA,gBACpDxI,OAAA;YACEyI,KAAK,EAAEf,eAAe,CAACtH,YAAY,KAAK,MAAM,CAAE;YAChD0I,OAAO,EAAEA,CAAA,KAAM;cACbzI,eAAe,CAAC,MAAM,CAAC;cACvB;YACF,CAAE;YAAAmI,QAAA,EACH;UAED;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1F,OAAA;YACEyI,KAAK,EAAEf,eAAe,CAACtH,YAAY,KAAK,OAAO,CAAE;YACjD0I,OAAO,EAAEA,CAAA,KAAM;cACbzI,eAAe,CAAC,OAAO,CAAC;cACxB;YACF,CAAE;YAAAmI,QAAA,EACH;UAED;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1F,OAAA;UAAM+I,QAAQ,EAAEjF,YAAa;UAAA0E,QAAA,GAC1B,CAAClI,OAAO,iBACPN,OAAA;YACEyI,KAAK,EAAET,UAAW;YAClBgB,IAAI,EAAC,MAAM;YACXnI,IAAI,EAAC,MAAM;YACXoI,WAAW,EAAC,WAAW;YACvB1F,KAAK,EAAElC,QAAQ,CAACR,IAAI,IAAI,EAAG;YAC3BqI,QAAQ,EAAE7F,iBAAkB;YAC5B8F,QAAQ;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACF,eAED1F,OAAA;YACEyI,KAAK,EAAET,UAAW;YAClBgB,IAAI,EAAC,OAAO;YACZnI,IAAI,EAAC,OAAO;YACZoI,WAAW,EAAC,eAAe;YAC3B1F,KAAK,EAAElC,QAAQ,CAACX,KAAM;YACtBwI,QAAQ,EAAE7F,iBAAkB;YAC5B8F,QAAQ;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF1F,OAAA;YACEyI,KAAK,EAAET,UAAW;YAClBgB,IAAI,EAAC,UAAU;YACfnI,IAAI,EAAC,UAAU;YACfoI,WAAW,EAAC,UAAU;YACtB1F,KAAK,EAAElC,QAAQ,CAACV,QAAS;YACzBuI,QAAQ,EAAE7F,iBAAkB;YAC5B8F,QAAQ;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EAGD,CAACpF,OAAO,IAAIsB,QAAQ,CAACE,SAAS,IAAI,CAACF,QAAQ,CAACG,aAAa,iBACxD/B,OAAA;YAAKyI,KAAK,EAAE;cAAErB,YAAY,EAAE;YAAO,CAAE;YAAAoB,QAAA,gBACnCxI,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBgB,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,mBAAmB;cAC/B1F,KAAK,EAAE3B,QAAQ,CAACI,OAAQ;cACxBkH,QAAQ,EAAExF,eAAgB;cAC1B0F,SAAS,EAAE,CAAE;cACbD,QAAQ;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF1F,OAAA;cAAKyI,KAAK,EAAE;gBACVtC,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,eAAe;gBAC/BD,UAAU,EAAE,QAAQ;gBACpBmC,SAAS,EAAE,QAAQ;gBACnBjB,QAAQ,EAAE;cACZ,CAAE;cAAAkB,QAAA,gBACAxI,OAAA;gBAAMyI,KAAK,EAAE;kBAAEZ,KAAK,EAAE;gBAAO,CAAE;gBAAAW,QAAA,GAAC,kBACd,EAACpG,IAAI,CAACiH,GAAG,CAAC,CAAC,EAAEjH,IAAI,CAACkH,IAAI,CAAC,CAAC1H,QAAQ,CAACM,SAAS,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,EAAC,MACrF;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP1F,OAAA;gBACEgJ,IAAI,EAAC,QAAQ;gBACbF,OAAO,EAAEjF,eAAgB;gBACzB4E,KAAK,EAAE;kBACL3C,UAAU,EAAE,MAAM;kBAClBe,MAAM,EAAE,MAAM;kBACdgB,KAAK,EAAE,SAAS;kBAChBC,MAAM,EAAE,SAAS;kBACjByB,cAAc,EAAE,WAAW;kBAC3BjC,QAAQ,EAAE;gBACZ,CAAE;gBAAAkB,QAAA,EACH;cAED;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA,CAACpF,OAAO,IAAIF,YAAY,KAAK,MAAM,iBAClCJ,OAAA,CAAAE,SAAA;YAAAsI,QAAA,gBACExI,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBgB,IAAI,EAAC,MAAM;cACXnI,IAAI,EAAC,WAAW;cAChBoI,WAAW,EAAC,YAAY;cACxB1F,KAAK,EAAGlC,QAAQ,CAAqBP,SAAS,IAAI,EAAG;cACrDoI,QAAQ,EAAE7F,iBAAkB;cAC5B8F,QAAQ;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF1F,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBnH,IAAI,EAAC,YAAY;cACjB0C,KAAK,EAAElC,QAAQ,CAACN,UAAU,IAAI,EAAG;cACjCmI,QAAQ,EAAE7F,iBAAkB;cAC5B8F,QAAQ;cAAAX,QAAA,gBAERxI,OAAA;gBAAQuD,KAAK,EAAC,EAAE;gBAAAiF,QAAA,EAAC;cAAiB;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C1F,OAAA;gBAAQuD,KAAK,EAAC,MAAM;gBAAAiF,QAAA,EAAC;cAA6C;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3E1F,OAAA;gBAAQuD,KAAK,EAAC,KAAK;gBAAAiF,QAAA,EAAC;cAAoC;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjE1F,OAAA;gBAAQuD,KAAK,EAAC,KAAK;gBAAAiF,QAAA,EAAC;cAA0C;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvE1F,OAAA;gBAAQuD,KAAK,EAAC,KAAK;gBAAAiF,QAAA,EAAC;cAA6C;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1E1F,OAAA;gBAAQuD,KAAK,EAAC,MAAM;gBAAAiF,QAAA,EAAC;cAA6B;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3D1F,OAAA;gBAAQuD,KAAK,EAAC,MAAM;gBAAAiF,QAAA,EAAC;cAA0C;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxE1F,OAAA;gBAAQuD,KAAK,EAAC,MAAM;gBAAAiF,QAAA,EAAC;cAAiD;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/E1F,OAAA;gBAAQuD,KAAK,EAAC,OAAO;gBAAAiF,QAAA,EAAC;cAAsB;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrD1F,OAAA;gBAAQuD,KAAK,EAAC,KAAK;gBAAAiF,QAAA,EAAC;cAA0C;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACT1F,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBnH,IAAI,EAAC,MAAM;cACX0C,KAAK,EAAGlC,QAAQ,CAAqBL,IAAI,IAAI,EAAG;cAChDkI,QAAQ,EAAE7F,iBAAkB;cAC5B8F,QAAQ;cAAAX,QAAA,gBAERxI,OAAA;gBAAQuD,KAAK,EAAC,EAAE;gBAAAiF,QAAA,EAAC;cAAW;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrC1F,OAAA;gBAAQuD,KAAK,EAAC,GAAG;gBAAAiF,QAAA,EAAC;cAAQ;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC1F,OAAA;gBAAQuD,KAAK,EAAC,GAAG;gBAAAiF,QAAA,EAAC;cAAQ;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC1F,OAAA;gBAAQuD,KAAK,EAAC,GAAG;gBAAAiF,QAAA,EAAC;cAAQ;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC1F,OAAA;gBAAQuD,KAAK,EAAC,GAAG;gBAAAiF,QAAA,EAAC;cAAQ;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACT1F,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBgB,IAAI,EAAC,KAAK;cACVnI,IAAI,EAAC,OAAO;cACZoI,WAAW,EAAC,yBAAyB;cACrC1F,KAAK,EAAElC,QAAQ,CAACH,KAAK,IAAI,EAAG;cAC5BgI,QAAQ,EAAE7F;YAAkB;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,eACF,CACH,EAEA,CAACpF,OAAO,IAAIF,YAAY,KAAK,OAAO,iBACnCJ,OAAA,CAAAE,SAAA;YAAAsI,QAAA,gBACExI,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBgB,IAAI,EAAC,MAAM;cACXnI,IAAI,EAAC,YAAY;cACjBoI,WAAW,EAAC,0CAA0C;cACtD1F,KAAK,EAAElC,QAAQ,CAACN,UAAU,IAAI,EAAG;cACjCmI,QAAQ,EAAE7F,iBAAkB;cAC5B8F,QAAQ;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF1F,OAAA;cACEyI,KAAK,EAAET,UAAW;cAClBgB,IAAI,EAAC,KAAK;cACVnI,IAAI,EAAC,OAAO;cACZoI,WAAW,EAAC,cAAc;cAC1B1F,KAAK,EAAElC,QAAQ,CAACH,KAAK,IAAI,EAAG;cAC5BgI,QAAQ,EAAE7F,iBAAkB;cAC5B8F,QAAQ;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACF,CACH,eAED1F,OAAA;YAAQgJ,IAAI,EAAC,QAAQ;YAACP,KAAK,EAAER,WAAY;YAACuB,QAAQ,EAAElI,SAAU;YAAAkH,QAAA,EAC3DlH,SAAS,GAAG,iBAAiB,GAC7BhB,OAAO,GAAG,iBAAiBF,YAAY,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO,EAAE,GAC1E,CAACwB,QAAQ,CAACE,SAAS,GAAG,aAAa,GACnC,CAACF,QAAQ,CAACG,aAAa,GAAG,eAAe,GACzC;UAA0B;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEP1F,OAAA;UACEyI,KAAK,EAAE;YACL3C,UAAU,EAAE,MAAM;YAClBe,MAAM,EAAE,MAAM;YACdgB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,SAAS;YACjBR,QAAQ,EAAE,QAAQ;YAClBP,KAAK,EAAE,MAAM;YACbR,OAAO,EAAE,MAAM;YACfgC,SAAS,EAAE;UACb,CAAE;UACFO,OAAO,EAAEA,CAAA,KAAM;YACbvI,UAAU,CAAC,CAACD,OAAO,CAAC;YACpBmB,UAAU,CAAC,EAAE,CAAC;YACd;YACAhB,kBAAkB,CAAC;cACjBC,KAAK,EAAE,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZC,IAAI,EAAE,MAAM;cACZC,IAAI,EAAE,EAAE;cACRC,SAAS,EAAE,EAAE;cACbC,UAAU,EAAE,EAAE;cACdC,IAAI,EAAEC,SAAS;cACfC,KAAK,EAAE;YACT,CAAC,CAAC;YACFE,gBAAgB,CAAC;cACfV,KAAK,EAAE,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZC,IAAI,EAAE,OAAO;cACbC,IAAI,EAAE,EAAE;cACRE,UAAU,EAAE,EAAE;cACdG,KAAK,EAAE;YACT,CAAC,CAAC;YACF;YACAW,WAAW,CAAC;cACVC,SAAS,EAAE,KAAK;cAChBC,aAAa,EAAE,KAAK;cACpBC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UAAAsG,QAAA,EAEDlI,OAAO,GAAG,sCAAsC,GAAG;QAAkC;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,EAIRlE,OAAO,iBAAIxB,OAAA;UAAKyI,KAAK,EAAEH,YAAa;UAAAE,QAAA,EAAEhH;QAAO;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAED,eAAevF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}