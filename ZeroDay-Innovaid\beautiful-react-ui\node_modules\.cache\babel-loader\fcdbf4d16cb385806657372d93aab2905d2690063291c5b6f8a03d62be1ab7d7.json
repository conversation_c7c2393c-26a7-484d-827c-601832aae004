{"ast": null, "code": "// Email service for sending OTP\n// This is a mock implementation - in production, integrate with EmailJS or similar service\nexport const sendOTPEmail=async request=>{try{// Mock email sending - replace with actual email service\nconsole.log('📧 Sending OTP Email:');console.log(`To: ${request.email}`);console.log(`OTP: ${request.otp}`);console.log(`Expires: ${request.expiryTime}`);// Simulate email sending delay\nawait new Promise(resolve=>setTimeout(resolve,1000));// In production, use EmailJS or similar service:\n/*\n    const emailjs = await import('emailjs-com');\n    \n    const templateParams = {\n      to_email: request.email,\n      to_name: request.name || 'User',\n      otp_code: request.otp,\n      expiry_time: request.expiryTime,\n      platform_name: 'InnovAid for SECE'\n    };\n\n    const result = await emailjs.send(\n      'YOUR_SERVICE_ID',\n      'YOUR_TEMPLATE_ID',\n      templateParams,\n      'YOUR_PUBLIC_KEY'\n    );\n\n    return result.status === 200;\n    */// Mock success\nreturn true;}catch(error){console.error('Error sending OTP email:',error);return false;}};export const getEmailTemplate=(otp,expiryTime)=>{return`\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\n        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }\n        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }\n        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }\n        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎓 InnovAid for SECE</h1>\n          <p>Email Verification Required</p>\n        </div>\n        <div class=\"content\">\n          <h2>Welcome to InnovAid!</h2>\n          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>\n          \n          <div class=\"otp-box\">\n            <p>Your verification code is:</p>\n            <div class=\"otp-code\">${otp}</div>\n          </div>\n          \n          <div class=\"warning\">\n            <strong>⚠️ Important:</strong>\n            <ul>\n              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>\n              <li>Do not share this code with anyone</li>\n              <li>If you didn't request this, please ignore this email</li>\n            </ul>\n          </div>\n          \n          <p>Once verified, you'll have access to all campus utilities including:</p>\n          <ul>\n            <li>📚 Library Management</li>\n            <li>🏠 Hostel Services</li>\n            <li>📅 Timetable Access</li>\n            <li>🚀 Tech Events & Updates</li>\n            <li>📋 Polls & Forms</li>\n            <li>🔍 Lost & Found</li>\n          </ul>\n          \n          <div class=\"footer\">\n            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}