{"ast": null, "code": "// Email service for sending <PERSON><PERSON> using EmailJS\nimport emailjs from '@emailjs/browser';\n// EmailJS Configuration - Updated with your actual credentials\nconst EMAILJS_CONFIG = {\n  SERVICE_ID: 'service_4bguc4b',\n  // Your Gmail service ID\n  TEMPLATE_ID: 'template_z52dikc',\n  // Your OTP template ID\n  PUBLIC_KEY: 'CbAorMO9c4FOexefV' // Your public key\n};\n\n// ✅ EmailJS is now configured with your actual credentials!\n\nexport const sendOTPEmail = async request => {\n  try {\n    console.log('📧 Preparing to send OTP Email to:', request.email);\n\n    // Always show OTP in console for development/testing\n    console.log('🔐 YOUR OTP CODE:', request.otp);\n    console.log('⏰ Expires:', request.expiryTime);\n    console.log('📋 Copy this OTP to complete registration');\n\n    // Create email content\n    const emailSubject = `InnovAid SECE - Your OTP Code: ${request.otp}`;\n    const emailBody = `\nHello ${request.name || 'User'},\n\nYour OTP for InnovAid SECE registration is: ${request.otp}\n\nThis code will expire at: ${request.expiryTime}\n\nPlease enter this code in the registration form to complete your account setup.\n\nIf you didn't request this, please ignore this email.\n\nBest regards,\nInnovAid Team\nSri Eshwar College of Engineering\n    `.trim();\n\n    // Try multiple email sending methods\n\n    // Method 1: Try EmailJS if configured\n    if (EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY') {\n      try {\n        const templateParams = {\n          to_email: request.email,\n          to_name: request.name || 'User',\n          otp_code: request.otp,\n          expiry_time: request.expiryTime,\n          platform_name: 'InnovAid for SECE',\n          college_name: 'Sri Eshwar College of Engineering'\n        };\n        const result = await emailjs.send(EMAILJS_CONFIG.SERVICE_ID, EMAILJS_CONFIG.TEMPLATE_ID, templateParams, EMAILJS_CONFIG.PUBLIC_KEY);\n        if (result.status === 200) {\n          console.log('✅ Email sent successfully via EmailJS');\n          return true;\n        }\n      } catch (emailError) {\n        console.warn('⚠️ EmailJS failed:', emailError);\n      }\n    }\n\n    // Method 2: Open default email client (fallback)\n    try {\n      const mailtoLink = `mailto:${request.email}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;\n\n      // Create a temporary link and click it\n      const link = document.createElement('a');\n      link.href = mailtoLink;\n      link.style.display = 'none';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      console.log('📧 Email client opened with OTP details');\n      console.log('📝 Please send the email manually or copy the OTP from console');\n    } catch (mailtoError) {\n      console.warn('⚠️ Could not open email client:', mailtoError);\n    }\n\n    // Method 3: Show alert with OTP (always works)\n    setTimeout(() => {\n      alert(`OTP for ${request.email}: ${request.otp}\\n\\nExpires: ${request.expiryTime}\\n\\nPlease copy this code and enter it in the registration form.`);\n    }, 500);\n\n    // Simulate email sending delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    console.log('✅ OTP delivery attempted via multiple methods');\n    console.log('🔗 To setup real email sending:');\n    console.log('1. Sign up at https://emailjs.com');\n    console.log('2. Create email service and template');\n    console.log('3. Update EMAILJS_CONFIG.PUBLIC_KEY in emailService.ts');\n    return true;\n  } catch (error) {\n    console.error('❌ Error in OTP delivery:', error);\n    // Even if email fails, show OTP in alert as fallback\n    alert(`OTP for registration: ${request.otp}\\n\\nExpires: ${request.expiryTime}`);\n    return true; // Return true since user can still get OTP from alert\n  }\n};\nexport const getEmailTemplate = (otp, expiryTime) => {\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\n        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }\n        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }\n        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }\n        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎓 InnovAid for SECE</h1>\n          <p>Email Verification Required</p>\n        </div>\n        <div class=\"content\">\n          <h2>Welcome to InnovAid!</h2>\n          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>\n          \n          <div class=\"otp-box\">\n            <p>Your verification code is:</p>\n            <div class=\"otp-code\">${otp}</div>\n          </div>\n          \n          <div class=\"warning\">\n            <strong>⚠️ Important:</strong>\n            <ul>\n              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>\n              <li>Do not share this code with anyone</li>\n              <li>If you didn't request this, please ignore this email</li>\n            </ul>\n          </div>\n          \n          <p>Once verified, you'll have access to all campus utilities including:</p>\n          <ul>\n            <li>📚 Library Management</li>\n            <li>🏠 Hostel Services</li>\n            <li>📅 Timetable Access</li>\n            <li>🚀 Tech Events & Updates</li>\n            <li>📋 Polls & Forms</li>\n            <li>🔍 Lost & Found</li>\n          </ul>\n          \n          <div class=\"footer\">\n            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n};", "map": {"version": 3, "names": ["emailjs", "EMAILJS_CONFIG", "SERVICE_ID", "TEMPLATE_ID", "PUBLIC_KEY", "sendOTPEmail", "request", "console", "log", "email", "otp", "expiryTime", "emailSubject", "emailBody", "name", "trim", "templateParams", "to_email", "to_name", "otp_code", "expiry_time", "platform_name", "college_name", "result", "send", "status", "emailError", "warn", "mailtoLink", "encodeURIComponent", "link", "document", "createElement", "href", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "mailtoError", "setTimeout", "alert", "Promise", "resolve", "error", "getEmailTemplate"], "sources": ["B:/ZERO DAY/ZeroDay-Innovaid/beautiful-react-ui/src/services/emailService.ts"], "sourcesContent": ["// Email service for sending <PERSON><PERSON> using EmailJS\nimport emailjs from '@emailjs/browser';\n\nexport interface EmailOTPRequest {\n  email: string;\n  otp: string;\n  name?: string;\n  expiryTime: string;\n}\n\n// EmailJS Configuration - Updated with your actual credentials\nconst EMAILJS_CONFIG = {\n  SERVICE_ID: 'service_4bguc4b',     // Your Gmail service ID\n  TEMPLATE_ID: 'template_z52dikc',   // Your OTP template ID\n  PUBLIC_KEY: 'CbAorMO9c4FOexefV'    // Your public key\n};\n\n// ✅ EmailJS is now configured with your actual credentials!\n\nexport const sendOTPEmail = async (request: EmailOTPRequest): Promise<boolean> => {\n  try {\n    console.log('📧 Preparing to send OTP Email to:', request.email);\n\n    // Always show OTP in console for development/testing\n    console.log('🔐 YOUR OTP CODE:', request.otp);\n    console.log('⏰ Expires:', request.expiryTime);\n    console.log('📋 Copy this OTP to complete registration');\n\n    // Create email content\n    const emailSubject = `InnovAid SECE - Your OTP Code: ${request.otp}`;\n    const emailBody = `\nHello ${request.name || 'User'},\n\nYour OTP for InnovAid SECE registration is: ${request.otp}\n\nThis code will expire at: ${request.expiryTime}\n\nPlease enter this code in the registration form to complete your account setup.\n\nIf you didn't request this, please ignore this email.\n\nBest regards,\nInnovAid Team\nSri Eshwar College of Engineering\n    `.trim();\n\n    // Try multiple email sending methods\n\n    // Method 1: Try EmailJS if configured\n    if (EMAILJS_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY') {\n      try {\n        const templateParams = {\n          to_email: request.email,\n          to_name: request.name || 'User',\n          otp_code: request.otp,\n          expiry_time: request.expiryTime,\n          platform_name: 'InnovAid for SECE',\n          college_name: 'Sri Eshwar College of Engineering'\n        };\n\n        const result = await emailjs.send(\n          EMAILJS_CONFIG.SERVICE_ID,\n          EMAILJS_CONFIG.TEMPLATE_ID,\n          templateParams,\n          EMAILJS_CONFIG.PUBLIC_KEY\n        );\n\n        if (result.status === 200) {\n          console.log('✅ Email sent successfully via EmailJS');\n          return true;\n        }\n      } catch (emailError) {\n        console.warn('⚠️ EmailJS failed:', emailError);\n      }\n    }\n\n    // Method 2: Open default email client (fallback)\n    try {\n      const mailtoLink = `mailto:${request.email}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;\n\n      // Create a temporary link and click it\n      const link = document.createElement('a');\n      link.href = mailtoLink;\n      link.style.display = 'none';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      console.log('📧 Email client opened with OTP details');\n      console.log('📝 Please send the email manually or copy the OTP from console');\n    } catch (mailtoError) {\n      console.warn('⚠️ Could not open email client:', mailtoError);\n    }\n\n    // Method 3: Show alert with OTP (always works)\n    setTimeout(() => {\n      alert(`OTP for ${request.email}: ${request.otp}\\n\\nExpires: ${request.expiryTime}\\n\\nPlease copy this code and enter it in the registration form.`);\n    }, 500);\n\n    // Simulate email sending delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    console.log('✅ OTP delivery attempted via multiple methods');\n    console.log('🔗 To setup real email sending:');\n    console.log('1. Sign up at https://emailjs.com');\n    console.log('2. Create email service and template');\n    console.log('3. Update EMAILJS_CONFIG.PUBLIC_KEY in emailService.ts');\n\n    return true;\n  } catch (error) {\n    console.error('❌ Error in OTP delivery:', error);\n    // Even if email fails, show OTP in alert as fallback\n    alert(`OTP for registration: ${request.otp}\\n\\nExpires: ${request.expiryTime}`);\n    return true; // Return true since user can still get OTP from alert\n  }\n};\n\nexport const getEmailTemplate = (otp: string, expiryTime: string): string => {\n  return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\n        .otp-box { background: white; border: 2px solid #667eea; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }\n        .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }\n        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }\n        .footer { text-align: center; color: #666; font-size: 14px; margin-top: 20px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎓 InnovAid for SECE</h1>\n          <p>Email Verification Required</p>\n        </div>\n        <div class=\"content\">\n          <h2>Welcome to InnovAid!</h2>\n          <p>Thank you for registering with InnovAid for SECE. To complete your registration, please verify your email address using the OTP below:</p>\n          \n          <div class=\"otp-box\">\n            <p>Your verification code is:</p>\n            <div class=\"otp-code\">${otp}</div>\n          </div>\n          \n          <div class=\"warning\">\n            <strong>⚠️ Important:</strong>\n            <ul>\n              <li>This OTP will expire at: <strong>${expiryTime}</strong></li>\n              <li>Do not share this code with anyone</li>\n              <li>If you didn't request this, please ignore this email</li>\n            </ul>\n          </div>\n          \n          <p>Once verified, you'll have access to all campus utilities including:</p>\n          <ul>\n            <li>📚 Library Management</li>\n            <li>🏠 Hostel Services</li>\n            <li>📅 Timetable Access</li>\n            <li>🚀 Tech Events & Updates</li>\n            <li>📋 Polls & Forms</li>\n            <li>🔍 Lost & Found</li>\n          </ul>\n          \n          <div class=\"footer\">\n            <p>Best regards,<br>InnovAid Team<br>Sri Eshwar College of Engineering</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n};\n"], "mappings": "AAAA;AACA,OAAOA,OAAO,MAAM,kBAAkB;AAStC;AACA,MAAMC,cAAc,GAAG;EACrBC,UAAU,EAAE,iBAAiB;EAAM;EACnCC,WAAW,EAAE,kBAAkB;EAAI;EACnCC,UAAU,EAAE,mBAAmB,CAAI;AACrC,CAAC;;AAED;;AAEA,OAAO,MAAMC,YAAY,GAAG,MAAOC,OAAwB,IAAuB;EAChF,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,OAAO,CAACG,KAAK,CAAC;;IAEhE;IACAF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,OAAO,CAACI,GAAG,CAAC;IAC7CH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,OAAO,CAACK,UAAU,CAAC;IAC7CJ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;IAExD;IACA,MAAMI,YAAY,GAAG,kCAAkCN,OAAO,CAACI,GAAG,EAAE;IACpE,MAAMG,SAAS,GAAG;AACtB,QAAQP,OAAO,CAACQ,IAAI,IAAI,MAAM;AAC9B;AACA,8CAA8CR,OAAO,CAACI,GAAG;AACzD;AACA,4BAA4BJ,OAAO,CAACK,UAAU;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAACI,IAAI,CAAC,CAAC;;IAER;;IAEA;IACA,IAAId,cAAc,CAACG,UAAU,KAAK,iBAAiB,EAAE;MACnD,IAAI;QACF,MAAMY,cAAc,GAAG;UACrBC,QAAQ,EAAEX,OAAO,CAACG,KAAK;UACvBS,OAAO,EAAEZ,OAAO,CAACQ,IAAI,IAAI,MAAM;UAC/BK,QAAQ,EAAEb,OAAO,CAACI,GAAG;UACrBU,WAAW,EAAEd,OAAO,CAACK,UAAU;UAC/BU,aAAa,EAAE,mBAAmB;UAClCC,YAAY,EAAE;QAChB,CAAC;QAED,MAAMC,MAAM,GAAG,MAAMvB,OAAO,CAACwB,IAAI,CAC/BvB,cAAc,CAACC,UAAU,EACzBD,cAAc,CAACE,WAAW,EAC1Ba,cAAc,EACdf,cAAc,CAACG,UACjB,CAAC;QAED,IAAImB,MAAM,CAACE,MAAM,KAAK,GAAG,EAAE;UACzBlB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpD,OAAO,IAAI;QACb;MACF,CAAC,CAAC,OAAOkB,UAAU,EAAE;QACnBnB,OAAO,CAACoB,IAAI,CAAC,oBAAoB,EAAED,UAAU,CAAC;MAChD;IACF;;IAEA;IACA,IAAI;MACF,MAAME,UAAU,GAAG,UAAUtB,OAAO,CAACG,KAAK,YAAYoB,kBAAkB,CAACjB,YAAY,CAAC,SAASiB,kBAAkB,CAAChB,SAAS,CAAC,EAAE;;MAE9H;MACA,MAAMiB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGL,UAAU;MACtBE,IAAI,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;MAC3BJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAE/BvB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;IAC/E,CAAC,CAAC,OAAOgC,WAAW,EAAE;MACpBjC,OAAO,CAACoB,IAAI,CAAC,iCAAiC,EAAEa,WAAW,CAAC;IAC9D;;IAEA;IACAC,UAAU,CAAC,MAAM;MACfC,KAAK,CAAC,WAAWpC,OAAO,CAACG,KAAK,KAAKH,OAAO,CAACI,GAAG,gBAAgBJ,OAAO,CAACK,UAAU,kEAAkE,CAAC;IACrJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA,MAAM,IAAIgC,OAAO,CAACC,OAAO,IAAIH,UAAU,CAACG,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvDrC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5DD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDD,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAErE,OAAO,IAAI;EACb,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACdtC,OAAO,CAACsC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD;IACAH,KAAK,CAAC,yBAAyBpC,OAAO,CAACI,GAAG,gBAAgBJ,OAAO,CAACK,UAAU,EAAE,CAAC;IAC/E,OAAO,IAAI,CAAC,CAAC;EACf;AACF,CAAC;AAED,OAAO,MAAMmC,gBAAgB,GAAGA,CAACpC,GAAW,EAAEC,UAAkB,KAAa;EAC3E,OAAO;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoCD,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA,qDAAqDC,UAAU;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}