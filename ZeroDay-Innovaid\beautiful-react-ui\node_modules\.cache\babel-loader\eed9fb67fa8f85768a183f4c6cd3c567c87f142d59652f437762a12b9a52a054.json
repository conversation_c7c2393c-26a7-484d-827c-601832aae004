{"ast": null, "code": "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n  return new Promise((resolve, reject) => {\n    const xhr = new XMLHttpRequest();\n    xhr.addEventListener('load', ({\n      target\n    }) => {\n      const responseStatus = new EmailJSResponseStatus(target);\n      if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n        resolve(responseStatus);\n      } else {\n        reject(responseStatus);\n      }\n    });\n    xhr.addEventListener('error', ({\n      target\n    }) => {\n      reject(new EmailJSResponseStatus(target));\n    });\n    xhr.open('POST', store._origin + url, true);\n    Object.keys(headers).forEach(key => {\n      xhr.setRequestHeader(key, headers[key]);\n    });\n    xhr.send(data);\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}