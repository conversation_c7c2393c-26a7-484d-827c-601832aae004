{"ast": null, "code": "export const validateParams = (userID, serviceID, templateID) => {\n  if (!userID) {\n    throw 'The user ID is required. Visit https://dashboard.emailjs.com/admin/integration';\n  }\n  if (!serviceID) {\n    throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n  }\n  if (!templateID) {\n    throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n  }\n  return true;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}