{"ast": null, "code": "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templatePrams - the template params, what will be set to the EmailJS template\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = (serviceID, templateID, templatePrams, userID) => {\n  const uID = userID || store._userID;\n  validateParams(uID, serviceID, templateID);\n  const params = {\n    lib_version: '3.2.0',\n    user_id: uID,\n    service_id: serviceID,\n    template_id: templateID,\n    template_params: templatePrams\n  };\n  return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n    'Content-type': 'application/json'\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}